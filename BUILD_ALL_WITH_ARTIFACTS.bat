@echo off
REM ====================================================================
REM COMPLETE OCR BUILD FOR AMD RYZEN 9 9950X3D BEAST
REM ====================================================================
REM Builds: OpenCV + Tesseract + Leptonica + Accelerator PYD
REM Keeps: .sln files, CMakeCache, build recipes, logs
REM ====================================================================

echo ============================================================
echo COMPLETE OCR BUILD WITH ALL ARTIFACTS
echo ============================================================
echo.
echo This will build EVERYTHING and keep:
echo - Solution files (.sln)
echo - CMake cache and recipes
echo - Build logs
echo - All intermediate files
echo.
echo BUILD ENV: Single-core VM (very slow)
echo TARGET: AMD Ryzen 9 9950X3D (32 threads)
echo.
echo ESTIMATED TIME: 4-6 HOURS TOTAL
echo.
pause

set OPENCV_VERSION=4.10.0
set TESSERACT_VERSION=5.5.1
set LEPTONICA_VERSION=1.85.0
set BUILD_DIR=C:\TANK\ocr_accelerator\source
set INSTALL_DIR=C:\TANK\ocr_accelerator\deps
set ARTIFACTS_DIR=C:\TANK\ocr_accelerator\build_artifacts
set VCPKG_BIN=C:\tools\vcpkg\installed\x64-windows\bin
set PYTHON_HOME=C:\Python311

REM Create artifacts directory
if not exist %ARTIFACTS_DIR% mkdir %ARTIFACTS_DIR%
if not exist %ARTIFACTS_DIR%\logs mkdir %ARTIFACTS_DIR%\logs

REM ====================================================================
REM PART 1: BUILD OPENCV (KEEP .SLN)
REM ====================================================================
echo.
echo ============================================================
echo PART 1 of 5: BUILDING OPENCV %OPENCV_VERSION%
echo ============================================================
echo.
echo This builds opencv_world DLL and keeps .sln file
echo Estimated time: 2-3 hours
echo.
pause

cd /d %BUILD_DIR%

if exist opencv\build\OpenCV.sln (
    echo OpenCV.sln already exists
    choice /C YN /T 10 /D N /M "Skip OpenCV build"
    if errorlevel 2 goto PART2_LEPTONICA
)

if not exist opencv\CMakeLists.txt (
    echo Downloading OpenCV %OPENCV_VERSION%...
    curl -L -o opencv.zip https://github.com/opencv/opencv/archive/refs/tags/%OPENCV_VERSION%.zip
    if %ERRORLEVEL% NEQ 0 (
        echo ERROR: Failed to download OpenCV!
        pause
        exit /b 1
    )

    echo Extracting OpenCV...
    tar -xf opencv.zip
    if exist opencv-%OPENCV_VERSION% (
        move opencv-%OPENCV_VERSION% opencv
    ) else (
        echo ERROR: Extraction failed!
        pause
        exit /b 1
    )
    del opencv.zip

    echo Downloading OpenCV contrib modules...
    curl -L -o opencv_contrib.zip https://github.com/opencv/opencv_contrib/archive/refs/tags/%OPENCV_VERSION%.zip
    tar -xf opencv_contrib.zip
    if exist opencv_contrib-%OPENCV_VERSION% (
        move opencv_contrib-%OPENCV_VERSION% opencv_contrib
    ) else (
        echo ERROR: Contrib extraction failed!
        pause
        exit /b 1
    )
    del opencv_contrib.zip
)

cd opencv
if not exist build mkdir build
cd build

echo.
echo Configuring OpenCV for 9950X3D...
echo Generating Visual Studio solution...
cmake .. -G "Visual Studio 17 2022" -A x64 ^
    -DCMAKE_BUILD_TYPE=Release ^
    -DCMAKE_INSTALL_PREFIX=%INSTALL_DIR%\opencv ^
    -DBUILD_opencv_world=ON ^
    -DBUILD_SHARED_LIBS=ON ^
    -DWITH_OPENMP=ON ^
    -DWITH_IPP=OFF ^
    -DCPU_BASELINE=SSE4_2 ^
    -DCPU_DISPATCH=SSE4_2,AVX,AVX2,FMA3 ^
    -DENABLE_FAST_MATH=ON ^
    -DBUILD_TESTS=OFF ^
    -DBUILD_PERF_TESTS=OFF ^
    -DBUILD_EXAMPLES=OFF ^
    -DBUILD_DOCS=OFF ^
    -DBUILD_opencv_apps=OFF ^
    -DBUILD_opencv_python2=OFF ^
    -DBUILD_opencv_python3=OFF ^
    -DOPENCV_EXTRA_MODULES_PATH=../../opencv_contrib/modules ^
    -DWITH_CUDA=OFF ^
    -DWITH_OPENCL=OFF ^
    -DENABLE_PRECOMPILED_HEADERS=OFF > %ARTIFACTS_DIR%\logs\opencv_cmake.log 2>&1

if %ERRORLEVEL% NEQ 0 (
    echo ERROR: OpenCV CMake configuration failed!
    echo Check log: %ARTIFACTS_DIR%\logs\opencv_cmake.log
    pause
    exit /b 1
)

echo.
echo [OK] OpenCV.sln generated
if exist OpenCV.sln (
    echo Copying OpenCV.sln to artifacts...
    copy OpenCV.sln %ARTIFACTS_DIR%\OpenCV.sln >nul
    copy CMakeCache.txt %ARTIFACTS_DIR%\OpenCV_CMakeCache.txt >nul
)

echo.
echo Building OpenCV (single thread, logging to file)...
echo Started at: %time%
echo Check progress in: %ARTIFACTS_DIR%\logs\opencv_build.log
cmake --build . --config Release --target INSTALL -- /m:1 /fl /flp:logfile=%ARTIFACTS_DIR%\logs\opencv_build.log

if %ERRORLEVEL% NEQ 0 (
    echo ERROR: OpenCV build failed!
    echo Check log: %ARTIFACTS_DIR%\logs\opencv_build.log
    pause
    exit /b 1
)

echo Completed at: %time%
echo [OK] OpenCV built successfully!

REM Copy build recipes
echo Copying OpenCV build recipes...
xcopy /Y "CMakeFiles\*.txt" "%ARTIFACTS_DIR%\opencv_recipes\" >nul 2>&1
xcopy /Y "modules\*.cmake" "%ARTIFACTS_DIR%\opencv_recipes\" >nul 2>&1

cd /d C:\TANK\ocr_accelerator
xcopy /Y "%INSTALL_DIR%\opencv\x64\vc17\bin\*.dll" "bin\" >nul 2>&1
xcopy /Y "%INSTALL_DIR%\opencv\x64\vc17\lib\*.lib" "deps\lib\" >nul 2>&1
xcopy /E /Y "%INSTALL_DIR%\opencv\include\*" "deps\include\" >nul 2>&1

echo.
echo OpenCV artifacts saved to: %ARTIFACTS_DIR%
dir %ARTIFACTS_DIR%\OpenCV.* /B
echo.
echo ============================================================
echo PART 1 COMPLETE - OpenCV built (.sln saved)
echo ============================================================
pause

:PART2_LEPTONICA
REM ====================================================================
REM PART 2: BUILD LEPTONICA (KEEP .SLN)
REM ====================================================================
echo.
echo ============================================================
echo PART 2 of 5: BUILDING LEPTONICA %LEPTONICA_VERSION%
echo ============================================================
echo.
echo Building Leptonica and keeping .sln file
echo Estimated time: 30-45 minutes
echo.
pause

cd /d %BUILD_DIR%

if exist leptonica\build\leptonica.sln (
    echo leptonica.sln already exists
    choice /C YN /T 10 /D N /M "Skip Leptonica build"
    if errorlevel 2 goto PART3_TESSERACT
)

if exist leptonica rmdir /s /q leptonica

echo Downloading Leptonica %LEPTONICA_VERSION%...
curl -L -o leptonica.zip https://github.com/DanBloomberg/leptonica/archive/refs/tags/%LEPTONICA_VERSION%.zip
if %ERRORLEVEL% NEQ 0 (
    echo ERROR: Failed to download Leptonica!
    pause
    exit /b 1
)

echo Extracting Leptonica...
tar -xf leptonica.zip
move leptonica-%LEPTONICA_VERSION% leptonica
del leptonica.zip

cd leptonica
mkdir build
cd build

echo.
echo Configuring Leptonica for 9950X3D...
echo Generating Visual Studio solution...
cmake .. -G "Visual Studio 17 2022" -A x64 ^
    -DCMAKE_BUILD_TYPE=Release ^
    -DCMAKE_INSTALL_PREFIX=%INSTALL_DIR%\leptonica ^
    -DBUILD_SHARED_LIBS=ON ^
    -DSW_BUILD=OFF ^
    -DCMAKE_CXX_FLAGS_RELEASE="/O2 /Ob2 /Oi /Ot /Oy /GT /GL /GS- /fp:fast /arch:AVX2" ^
    -DCMAKE_C_FLAGS_RELEASE="/O2 /Ob2 /Oi /Ot /Oy /GT /GL /GS- /fp:fast /arch:AVX2" > %ARTIFACTS_DIR%\logs\leptonica_cmake.log 2>&1

if %ERRORLEVEL% NEQ 0 (
    echo ERROR: Leptonica CMake configuration failed!
    echo Check log: %ARTIFACTS_DIR%\logs\leptonica_cmake.log
    pause
    exit /b 1
)

echo [OK] leptonica.sln generated
if exist leptonica.sln (
    echo Copying leptonica.sln to artifacts...
    copy leptonica.sln %ARTIFACTS_DIR%\leptonica.sln >nul
    copy CMakeCache.txt %ARTIFACTS_DIR%\leptonica_CMakeCache.txt >nul
)

echo.
echo Building Leptonica (single thread, logging)...
echo Started at: %time%
cmake --build . --config Release --target INSTALL -- /m:1 /fl /flp:logfile=%ARTIFACTS_DIR%\logs\leptonica_build.log

if %ERRORLEVEL% NEQ 0 (
    echo ERROR: Leptonica build failed!
    echo Check log: %ARTIFACTS_DIR%\logs\leptonica_build.log
    pause
    exit /b 1
)

echo Completed at: %time%
echo [OK] Leptonica built successfully!

REM Copy build recipes
echo Copying Leptonica build recipes...
xcopy /Y "CMakeFiles\*.txt" "%ARTIFACTS_DIR%\leptonica_recipes\" >nul 2>&1

REM Create CMake config for Tesseract
if not exist "%INSTALL_DIR%\leptonica\lib\cmake\leptonica" (
    mkdir "%INSTALL_DIR%\leptonica\lib\cmake\leptonica"
)

(
echo # Leptonica CMake Config
echo set(Leptonica_INCLUDE_DIRS "%INSTALL_DIR%\leptonica\include"^)
echo set(Leptonica_LIBRARIES "%INSTALL_DIR%\leptonica\lib\leptonica-1.85.0.lib"^)
echo set(Leptonica_FOUND TRUE^)
) > "%INSTALL_DIR%\leptonica\lib\cmake\leptonica\LeptonicaConfig.cmake"

echo.
echo Leptonica artifacts saved to: %ARTIFACTS_DIR%
dir %ARTIFACTS_DIR%\leptonica.* /B
echo.
echo ============================================================
echo PART 2 COMPLETE - Leptonica built (.sln saved)
echo ============================================================
pause

:PART3_TESSERACT
REM ====================================================================
REM PART 3: BUILD TESSERACT (KEEP .SLN)
REM ====================================================================
echo.
echo ============================================================
echo PART 3 of 5: BUILDING TESSERACT %TESSERACT_VERSION%
echo ============================================================
echo.
echo Building Tesseract and keeping .sln file
echo Estimated time: 45-60 minutes
echo.
pause

cd /d %BUILD_DIR%

if exist tesseract\build\tesseract.sln (
    echo tesseract.sln already exists
    choice /C YN /T 10 /D N /M "Skip Tesseract build"
    if errorlevel 2 goto PART4_DLLS
)

if exist tesseract rmdir /s /q tesseract

echo Downloading Tesseract %TESSERACT_VERSION%...
curl -L -o tesseract.zip https://github.com/tesseract-ocr/tesseract/archive/refs/tags/%TESSERACT_VERSION%.zip
if %ERRORLEVEL% NEQ 0 (
    echo ERROR: Failed to download Tesseract!
    pause
    exit /b 1
)

echo Extracting Tesseract...
tar -xf tesseract.zip
move tesseract-%TESSERACT_VERSION% tesseract
del tesseract.zip

cd tesseract
mkdir build
cd build

echo.
echo Configuring Tesseract for 9950X3D...
echo Generating Visual Studio solution...
cmake .. -G "Visual Studio 17 2022" -A x64 ^
    -DCMAKE_BUILD_TYPE=Release ^
    -DCMAKE_INSTALL_PREFIX=%INSTALL_DIR%\tesseract ^
    -DBUILD_SHARED_LIBS=ON ^
    -DLeptonica_DIR=%INSTALL_DIR%\leptonica\lib\cmake\leptonica ^
    -DCMAKE_PREFIX_PATH=%INSTALL_DIR%\leptonica ^
    -DSW_BUILD=OFF ^
    -DBUILD_TRAINING_TOOLS=OFF ^
    -DBUILD_TESTS=OFF ^
    -DDISABLED_LEGACY_ENGINE=OFF ^
    -DOPENMP_BUILD=ON ^
    -DCMAKE_CXX_FLAGS_RELEASE="/O2 /Ob2 /Oi /Ot /Oy /GT /GL /GS- /fp:fast /arch:AVX2 /openmp" ^
    -DCMAKE_C_FLAGS_RELEASE="/O2 /Ob2 /Oi /Ot /Oy /GT /GL /GS- /fp:fast /arch:AVX2 /openmp" > %ARTIFACTS_DIR%\logs\tesseract_cmake.log 2>&1

if %ERRORLEVEL% NEQ 0 (
    echo ERROR: Tesseract CMake configuration failed!
    echo Check log: %ARTIFACTS_DIR%\logs\tesseract_cmake.log
    pause
    exit /b 1
)

echo [OK] tesseract.sln generated
if exist tesseract.sln (
    echo Copying tesseract.sln to artifacts...
    copy tesseract.sln %ARTIFACTS_DIR%\tesseract.sln >nul
    copy CMakeCache.txt %ARTIFACTS_DIR%\tesseract_CMakeCache.txt >nul
)

echo.
echo Building Tesseract (single thread, logging)...
echo Started at: %time%
cmake --build . --config Release --target INSTALL -- /m:1 /fl /flp:logfile=%ARTIFACTS_DIR%\logs\tesseract_build.log

if %ERRORLEVEL% NEQ 0 (
    echo ERROR: Tesseract build failed!
    echo Check log: %ARTIFACTS_DIR%\logs\tesseract_build.log
    pause
    exit /b 1
)

echo Completed at: %time%
echo [OK] Tesseract built successfully!

REM Copy build recipes
echo Copying Tesseract build recipes...
xcopy /Y "CMakeFiles\*.txt" "%ARTIFACTS_DIR%\tesseract_recipes\" >nul 2>&1

echo.
echo Downloading OCR language data...
cd /d %INSTALL_DIR%\tesseract\bin
if not exist tessdata mkdir tessdata
curl -L -o tessdata\eng.traineddata https://github.com/tesseract-ocr/tessdata_best/raw/main/eng.traineddata
curl -L -o tessdata\osd.traineddata https://github.com/tesseract-ocr/tessdata_best/raw/main/osd.traineddata

echo.
echo Tesseract artifacts saved to: %ARTIFACTS_DIR%
dir %ARTIFACTS_DIR%\tesseract.* /B
echo.
echo ============================================================
echo PART 3 COMPLETE - Tesseract built (.sln saved)
echo ============================================================
pause

:PART4_DLLS
REM ====================================================================
REM PART 4: COPY ALL DLLS AND CREATE MANIFEST
REM ====================================================================
echo.
echo ============================================================
echo PART 4 of 5: INSTALLING ALL DLLS
echo ============================================================
echo.
echo Copying all DLLs and creating dependency manifest
echo.
pause

cd /d C:\TANK\ocr_accelerator

echo Creating DLL manifest...
echo # DLL Manifest - %date% %time% > %ARTIFACTS_DIR%\dll_manifest.txt
echo. >> %ARTIFACTS_DIR%\dll_manifest.txt

echo Copying OpenCV DLLs...
xcopy /Y "%INSTALL_DIR%\opencv\x64\vc17\bin\*.dll" "bin\" >nul 2>&1
dir "%INSTALL_DIR%\opencv\x64\vc17\bin\*.dll" /B >> %ARTIFACTS_DIR%\dll_manifest.txt 2>nul

echo Copying Leptonica DLLs...
xcopy /Y "%INSTALL_DIR%\leptonica\bin\*.dll" "bin\" >nul 2>&1
dir "%INSTALL_DIR%\leptonica\bin\*.dll" /B >> %ARTIFACTS_DIR%\dll_manifest.txt 2>nul

echo Copying Tesseract DLLs...
xcopy /Y "%INSTALL_DIR%\tesseract\bin\*.dll" "bin\" >nul 2>&1
dir "%INSTALL_DIR%\tesseract\bin\*.dll" /B >> %ARTIFACTS_DIR%\dll_manifest.txt 2>nul

echo Copying libraries...
xcopy /Y "%INSTALL_DIR%\opencv\x64\vc17\lib\*.lib" "deps\lib\" >nul 2>&1
xcopy /Y "%INSTALL_DIR%\leptonica\lib\*.lib" "deps\lib\" >nul 2>&1
xcopy /Y "%INSTALL_DIR%\tesseract\lib\*.lib" "deps\lib\" >nul 2>&1

echo Copying headers...
xcopy /E /Y "%INSTALL_DIR%\opencv\include\*" "deps\include\" >nul 2>&1
xcopy /E /Y "%INSTALL_DIR%\leptonica\include\*" "deps\include\" >nul 2>&1
xcopy /E /Y "%INSTALL_DIR%\tesseract\include\*" "deps\include\" >nul 2>&1

echo Copying tessdata...
xcopy /E /Y "%INSTALL_DIR%\tesseract\bin\tessdata\*" "bin\tessdata\" >nul 2>&1

echo.
echo Copying IMAGE FORMAT DLLs from vcpkg...
echo # Image Format DLLs from vcpkg >> %ARTIFACTS_DIR%\dll_manifest.txt
cd bin

set IMAGE_DLLS=libpng16.dll jpeg62.dll turbojpeg.dll tiff.dll gif.dll openjp2.dll libwebp.dll libwebpdecoder.dll libwebpdemux.dll libwebpmux.dll zlib1.dll liblzma.dll bz2.dll libsharpyuv.dll archive.dll lz4.dll zstd.dll libcrypto-3-x64.dll libssl-3-x64.dll libcurl.dll sqlite3.dll libexpat.dll

for %%D in (%IMAGE_DLLS%) do (
    if exist "%VCPKG_BIN%\%%D" (
        copy /Y "%VCPKG_BIN%\%%D" . >nul
        echo [COPIED] %%D
        echo %%D >> %ARTIFACTS_DIR%\dll_manifest.txt
    )
)

REM Try alternate names
xcopy /Y "%VCPKG_BIN%\png*.dll" . >nul 2>&1
xcopy /Y "%VCPKG_BIN%\jpeg*.dll" . >nul 2>&1
xcopy /Y "%VCPKG_BIN%\tiff*.dll" . >nul 2>&1
xcopy /Y "%VCPKG_BIN%\zlib*.dll" . >nul 2>&1

echo.
echo Creating final DLL inventory...
echo # Final DLL Inventory in bin\ >> %ARTIFACTS_DIR%\dll_manifest.txt
dir *.dll /B >> %ARTIFACTS_DIR%\dll_manifest.txt

echo.
echo DLL manifest saved to: %ARTIFACTS_DIR%\dll_manifest.txt
echo.
echo ============================================================
echo PART 4 COMPLETE - All DLLs installed (manifest created)
echo ============================================================
pause

REM ====================================================================
REM PART 5: BUILD ACCELERATOR_SHAREDMEM.PYD (CREATE .VCXPROJ)
REM ====================================================================
echo.
echo ============================================================
echo PART 5 of 5: BUILDING ACCELERATOR_SHAREDMEM.PYD
echo ============================================================
echo.
echo Building Python module and creating project file
echo.
pause

cd /d C:\TANK\ocr_accelerator

if not exist accelerator_sharedmem.cpp (
    echo ERROR: accelerator_sharedmem.cpp not found!
    echo Please copy the source file to C:\TANK\ocr_accelerator\
    pause
    exit /b 1
)

echo Installing Python dependencies...
%PYTHON_HOME%\python.exe -m pip install pybind11 numpy >nul 2>&1

echo Getting Python paths...
for /f "delims=" %%i in ('%PYTHON_HOME%\python.exe -c "import sysconfig; print(sysconfig.get_path('include'))"') do set PYTHON_INCLUDE=%%i
for /f "delims=" %%i in ('%PYTHON_HOME%\python.exe -c "import pybind11; print(pybind11.get_include())"') do set PYBIND11_INCLUDE=%%i
for /f "delims=" %%i in ('%PYTHON_HOME%\python.exe -c "import numpy; print(numpy.get_include())"') do set NUMPY_INCLUDE=%%i

echo.
echo Creating build recipe for accelerator...
(
echo # Build Recipe for accelerator_sharedmem.pyd
echo # Generated: %date% %time%
echo.
echo ## Compiler Command:
echo cl /MD /O2 /Ob2 /Oi /Ot /Oy /GT /GL /GS- /fp:fast /arch:AVX2
echo    /I"%PYTHON_INCLUDE%"
echo    /I"%PYBIND11_INCLUDE%"
echo    /I"%NUMPY_INCLUDE%"
echo    /I"deps\include"
echo    /I"deps\opencv\include"
echo    /I"deps\tesseract\include"
echo    /I"deps\leptonica\include"
echo    /D_CRT_SECURE_NO_WARNINGS
echo    /DNDEBUG
echo    /c accelerator_sharedmem.cpp
echo.
echo ## Linker Command:
echo link /DLL /OUT:bin\accelerator_sharedmem.pyd
echo    /LIBPATH:"%PYTHON_HOME%\libs"
echo    /LIBPATH:"deps\lib"
echo    /LIBPATH:"deps\opencv\x64\vc17\lib"
echo    /LIBPATH:"deps\tesseract\lib"
echo    /LIBPATH:"deps\leptonica\lib"
echo    accelerator_sharedmem.obj
echo    python311.lib opencv_world*.lib tesseract*.lib leptonica*.lib
echo    /LTCG
) > %ARTIFACTS_DIR%\accelerator_build_recipe.txt

echo.
echo Compiling accelerator_sharedmem.cpp...
cl /MD /O2 /Ob2 /Oi /Ot /Oy /GT /GL /GS- /fp:fast /arch:AVX2 ^
    /I"%PYTHON_INCLUDE%" ^
    /I"%PYBIND11_INCLUDE%" ^
    /I"%NUMPY_INCLUDE%" ^
    /I"deps\include" ^
    /I"deps\opencv\include" ^
    /I"deps\tesseract\include" ^
    /I"deps\leptonica\include" ^
    /D_CRT_SECURE_NO_WARNINGS ^
    /DNDEBUG ^
    /c accelerator_sharedmem.cpp > %ARTIFACTS_DIR%\logs\accelerator_compile.log 2>&1

if %ERRORLEVEL% NEQ 0 (
    echo ERROR: Compilation failed!
    echo Check log: %ARTIFACTS_DIR%\logs\accelerator_compile.log
    pause
    exit /b 1
)

echo Linking to create .pyd...
link /DLL /OUT:bin\accelerator_sharedmem.pyd ^
    /LIBPATH:"%PYTHON_HOME%\libs" ^
    /LIBPATH:"deps\lib" ^
    /LIBPATH:"deps\opencv\x64\vc17\lib" ^
    /LIBPATH:"deps\tesseract\lib" ^
    /LIBPATH:"deps\leptonica\lib" ^
    accelerator_sharedmem.obj ^
    python311.lib ^
    opencv_world*.lib ^
    tesseract*.lib ^
    leptonica*.lib ^
    /LTCG > %ARTIFACTS_DIR%\logs\accelerator_link.log 2>&1

if %ERRORLEVEL% NEQ 0 (
    echo ERROR: Linking failed!
    echo Check log: %ARTIFACTS_DIR%\logs\accelerator_link.log
    pause
    exit /b 1
)

REM Keep the .obj file as artifact
copy accelerator_sharedmem.obj %ARTIFACTS_DIR%\ >nul
echo [OK] accelerator_sharedmem.pyd built!

echo.
echo ============================================================
echo PART 5 COMPLETE - PYD module built (recipe saved)
echo ============================================================
pause

REM ====================================================================
REM FINAL TEST AND ARTIFACT SUMMARY
REM ====================================================================
echo.
echo ============================================================
echo FINAL TEST AND ARTIFACT SUMMARY
echo ============================================================
echo.

cd bin
%PYTHON_HOME%\python.exe -c "import accelerator_sharedmem; print('[SUCCESS] Module loads correctly!')" 2>&1

if %ERRORLEVEL% NEQ 0 (
    echo [FAILED] Module won't load!
    %PYTHON_HOME%\python.exe -c "import accelerator_sharedmem" 2>&1 >> %ARTIFACTS_DIR%\logs\load_error.log
    echo Error logged to: %ARTIFACTS_DIR%\logs\load_error.log
) else (
    echo [SUCCESS] Module working!
)

echo.
echo ============================================================
echo BUILD ARTIFACTS SUMMARY
echo ============================================================
echo.
echo Location: %ARTIFACTS_DIR%
echo.
echo Solution Files:
dir %ARTIFACTS_DIR%\*.sln /B 2>nul
echo.
echo CMake Caches:
dir %ARTIFACTS_DIR%\*CMakeCache.txt /B 2>nul
echo.
echo Build Logs:
dir %ARTIFACTS_DIR%\logs\*.log /B 2>nul
echo.
echo Build Recipes:
dir %ARTIFACTS_DIR%\*recipe*.txt /B 2>nul
echo.
echo DLL Manifest:
if exist %ARTIFACTS_DIR%\dll_manifest.txt echo dll_manifest.txt
echo.
echo ============================================================
echo COMPLETE BUILD WITH ALL ARTIFACTS PRESERVED!
echo ============================================================
echo.
echo All components built:
echo - OpenCV %OPENCV_VERSION% (.sln preserved)
echo - Leptonica %LEPTONICA_VERSION% (.sln preserved)
echo - Tesseract %TESSERACT_VERSION% (.sln preserved)
echo - accelerator_sharedmem.pyd (recipe saved)
echo - All image format DLLs (manifest created)
echo.
echo Ready for deployment to BEAST!
echo.
pause