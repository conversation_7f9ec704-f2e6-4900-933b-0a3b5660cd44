@echo off
REM ====================================================================
REM PRE-BUILD VERIFICATION SCRIPT - MISSION CRITICAL
REM ====================================================================
REM Verifies EVERYTHING before attempting the build
REM ====================================================================

echo ============================================================
echo PRE-BUILD VERIFICATION FOR 9950X3D BUILD
echo ============================================================
echo.
echo This script verifies ALL prerequisites BEFORE building
echo to prevent any failures during the actual build process.
echo.
pause

set ERRORS=0
set WARNINGS=0

REM ====================================================================
REM SECTION 1: VERIFY SYSTEM REQUIREMENTS
REM ====================================================================
echo.
echo ============================================================
echo SECTION 1: SYSTEM REQUIREMENTS
echo ============================================================
echo.

REM Check Visual Studio 2022
echo Checking Visual Studio 2022...
where cl.exe >nul 2>&1
if %ERRORLEVEL% NEQ 0 (
    echo [ERROR] cl.exe not found! Visual Studio 2022 not in PATH
    echo        Run from "x64 Native Tools Command Prompt for VS 2022"
    set /a ERRORS+=1
) else (
    cl.exe 2>&1 | findstr "19.3" >nul
    if %ERRORLEVEL% EQU 0 (
        echo [OK] Visual Studio 2022 compiler found
    ) else (
        echo [WARNING] Compiler found but may not be VS2022
        set /a WARNINGS+=1
    )
)

REM Check CMake
echo Checking CMake...
where cmake.exe >nul 2>&1
if %ERRORLEVEL% NEQ 0 (
    echo [ERROR] CMake not found in PATH!
    set /a ERRORS+=1
) else (
    cmake --version | findstr "3.1" >nul
    if %ERRORLEVEL% EQU 0 (
        echo [OK] CMake found
    ) else (
        echo [WARNING] CMake version may be old
        set /a WARNINGS+=1
    )
)

REM Check Python 3.11
echo Checking Python 3.11...
if exist "C:\Python311\python.exe" (
    C:\Python311\python.exe --version 2>&1 | findstr "3.11" >nul
    if %ERRORLEVEL% EQU 0 (
        echo [OK] Python 3.11 found
    ) else (
        echo [ERROR] C:\Python311\python.exe exists but not version 3.11
        set /a ERRORS+=1
    )
) else (
    echo [ERROR] Python 3.11 not found at C:\Python311\
    set /a ERRORS+=1
)

REM Check vcpkg
echo Checking vcpkg...
if exist "C:\tools\vcpkg\vcpkg.exe" (
    echo [OK] vcpkg found
    if exist "C:\tools\vcpkg\installed\x64-windows\bin" (
        echo [OK] vcpkg x64-windows packages directory exists
    ) else (
        echo [WARNING] vcpkg x64-windows packages not installed
        set /a WARNINGS+=1
    )
) else (
    echo [ERROR] vcpkg not found at C:\tools\vcpkg\
    set /a ERRORS+=1
)

REM Check curl
echo Checking curl for downloads...
where curl.exe >nul 2>&1
if %ERRORLEVEL% NEQ 0 (
    echo [ERROR] curl.exe not found! Cannot download sources
    set /a ERRORS+=1
) else (
    echo [OK] curl found
)

REM Check tar
echo Checking tar for extraction...
where tar.exe >nul 2>&1
if %ERRORLEVEL% NEQ 0 (
    echo [ERROR] tar.exe not found! Cannot extract archives
    set /a ERRORS+=1
) else (
    echo [OK] tar found
)

REM ====================================================================
REM SECTION 2: VERIFY DIRECTORY STRUCTURE
REM ====================================================================
echo.
echo ============================================================
echo SECTION 2: DIRECTORY STRUCTURE
echo ============================================================
echo.

set PROJECT_DIR=C:\TANK\ocr_accelerator
set BUILD_DIR=%PROJECT_DIR%\source
set INSTALL_DIR=%PROJECT_DIR%\deps

echo Checking project directories...
if not exist "%PROJECT_DIR%" (
    echo [INFO] Creating %PROJECT_DIR%
    mkdir "%PROJECT_DIR%"
)

if not exist "%BUILD_DIR%" (
    echo [INFO] Creating %BUILD_DIR%
    mkdir "%BUILD_DIR%"
)

if not exist "%INSTALL_DIR%" (
    echo [INFO] Creating %INSTALL_DIR%
    mkdir "%INSTALL_DIR%"
)

if not exist "%PROJECT_DIR%\bin" (
    echo [INFO] Creating %PROJECT_DIR%\bin
    mkdir "%PROJECT_DIR%\bin"
)

if not exist "%PROJECT_DIR%\deps\lib" (
    echo [INFO] Creating %PROJECT_DIR%\deps\lib
    mkdir "%PROJECT_DIR%\deps\lib"
)

if not exist "%PROJECT_DIR%\deps\include" (
    echo [INFO] Creating %PROJECT_DIR%\deps\include
    mkdir "%PROJECT_DIR%\deps\include"
)

echo [OK] Directory structure ready

REM ====================================================================
REM SECTION 3: VERIFY SOURCE FILE
REM ====================================================================
echo.
echo ============================================================
echo SECTION 3: SOURCE CODE VERIFICATION
echo ============================================================
echo.

echo Checking for accelerator_sharedmem.cpp...
if exist "%PROJECT_DIR%\accelerator_sharedmem.cpp" (
    echo [OK] accelerator_sharedmem.cpp found
    for %%F in ("%PROJECT_DIR%\accelerator_sharedmem.cpp") do echo      Size: %%~zF bytes
) else (
    echo [ERROR] accelerator_sharedmem.cpp NOT FOUND!
    echo        Copy from GitHub repo or backup
    set /a ERRORS+=1
)

REM ====================================================================
REM SECTION 4: VERIFY PYTHON DEPENDENCIES
REM ====================================================================
echo.
echo ============================================================
echo SECTION 4: PYTHON DEPENDENCIES
echo ============================================================
echo.

if exist "C:\Python311\python.exe" (
    echo Checking pybind11...
    C:\Python311\python.exe -c "import pybind11; print('[OK] pybind11 version:', pybind11.__version__)" 2>nul
    if %ERRORLEVEL% NEQ 0 (
        echo [WARNING] pybind11 not installed - will install during build
        set /a WARNINGS+=1
    )
    
    echo Checking numpy...
    C:\Python311\python.exe -c "import numpy; print('[OK] numpy version:', numpy.__version__)" 2>nul
    if %ERRORLEVEL% NEQ 0 (
        echo [WARNING] numpy not installed - will install during build
        set /a WARNINGS+=1
    )
)

REM ====================================================================
REM SECTION 5: VERIFY DOWNLOAD URLS (TEST CONNECTIVITY)
REM ====================================================================
echo.
echo ============================================================
echo SECTION 5: DOWNLOAD URL VERIFICATION
echo ============================================================
echo.

echo Testing GitHub connectivity...
curl -I -s https://github.com >nul 2>&1
if %ERRORLEVEL% NEQ 0 (
    echo [ERROR] Cannot reach GitHub!
    set /a ERRORS+=1
) else (
    echo [OK] GitHub accessible
)

echo Testing OpenCV download URL...
curl -I -s https://github.com/opencv/opencv/archive/refs/tags/4.10.0.zip | findstr "HTTP" >nul 2>&1
if %ERRORLEVEL% NEQ 0 (
    echo [WARNING] OpenCV URL may have issues
    set /a WARNINGS+=1
) else (
    echo [OK] OpenCV URL verified
)

echo Testing Tesseract download URL...
curl -I -s https://github.com/tesseract-ocr/tesseract/archive/refs/tags/5.5.1.zip | findstr "HTTP" >nul 2>&1
if %ERRORLEVEL% NEQ 0 (
    echo [WARNING] Tesseract URL may have issues
    set /a WARNINGS+=1
) else (
    echo [OK] Tesseract URL verified
)

echo Testing Leptonica download URL...
curl -I -s https://github.com/DanBloomberg/leptonica/archive/refs/tags/1.85.0.zip | findstr "HTTP" >nul 2>&1
if %ERRORLEVEL% NEQ 0 (
    echo [WARNING] Leptonica URL may have issues
    set /a WARNINGS+=1
) else (
    echo [OK] Leptonica URL verified
)

REM ====================================================================
REM SECTION 6: VERIFY VCPKG IMAGE LIBRARIES
REM ====================================================================
echo.
echo ============================================================
echo SECTION 6: IMAGE FORMAT DLLS IN VCPKG
echo ============================================================
echo.

set VCPKG_BIN=C:\tools\vcpkg\installed\x64-windows\bin
if exist "%VCPKG_BIN%" (
    echo Checking essential image libraries...
    
    if exist "%VCPKG_BIN%\libpng16.dll" (
        echo [OK] libpng16.dll found
    ) else (
        echo [WARNING] libpng16.dll missing - OCR may fail on PNG
        set /a WARNINGS+=1
    )
    
    if exist "%VCPKG_BIN%\jpeg62.dll" (
        echo [OK] jpeg62.dll found
    ) else (
        echo [WARNING] jpeg62.dll missing - OCR may fail on JPEG
        set /a WARNINGS+=1
    )
    
    if exist "%VCPKG_BIN%\tiff.dll" (
        echo [OK] tiff.dll found
    ) else (
        echo [WARNING] tiff.dll missing - OCR may fail on TIFF
        set /a WARNINGS+=1
    )
    
    if exist "%VCPKG_BIN%\zlib1.dll" (
        echo [OK] zlib1.dll found
    ) else (
        echo [WARNING] zlib1.dll missing - compression issues
        set /a WARNINGS+=1
    )
) else (
    echo [ERROR] vcpkg bin directory not found!
    set /a ERRORS+=1
)

REM ====================================================================
REM SECTION 7: VERIFY COMPILER FLAGS
REM ====================================================================
echo.
echo ============================================================
echo SECTION 7: COMPILER FLAGS VERIFICATION
echo ============================================================
echo.

echo Testing /arch:AVX512 support...
echo int main() { return 0; } > test.cpp
cl.exe /c /arch:AVX512 test.cpp >nul 2>&1
if %ERRORLEVEL% NEQ 0 (
    echo [ERROR] Compiler doesn't support /arch:AVX512!
    echo        Visual Studio 2022 required
    set /a ERRORS+=1
) else (
    echo [OK] /arch:AVX512 supported
)

echo Testing /favor:AMD64 support...
cl.exe /c /favor:AMD64 test.cpp >nul 2>&1
if %ERRORLEVEL% NEQ 0 (
    echo [WARNING] /favor:AMD64 not supported
    set /a WARNINGS+=1
) else (
    echo [OK] /favor:AMD64 supported
)

echo Testing /Ob3 support...
cl.exe /c /Ob3 test.cpp >nul 2>&1
if %ERRORLEVEL% NEQ 0 (
    echo [WARNING] /Ob3 not supported (need newer VS)
    set /a WARNINGS+=1
) else (
    echo [OK] /Ob3 aggressive inlining supported
)

del test.cpp >nul 2>&1
del test.obj >nul 2>&1

REM ====================================================================
REM SECTION 8: CHECK EXISTING BUILDS
REM ====================================================================
echo.
echo ============================================================
echo SECTION 8: EXISTING BUILD CHECK
echo ============================================================
echo.

if exist "%BUILD_DIR%\opencv\build\OpenCV.sln" (
    echo [INFO] OpenCV already has build files
    echo        Will skip download unless cleaned
)

if exist "%BUILD_DIR%\leptonica\build" (
    echo [INFO] Leptonica already has build files
    echo        Will be cleaned before rebuild
)

if exist "%BUILD_DIR%\tesseract\build" (
    echo [INFO] Tesseract already has build files
    echo        Will be cleaned before rebuild
)

if exist "%PROJECT_DIR%\bin\opencv_world*.dll" (
    echo [INFO] OpenCV world DLL already exists:
    dir /B "%PROJECT_DIR%\bin\opencv_world*.dll"
)

if exist "%PROJECT_DIR%\bin\tesseract*.dll" (
    echo [INFO] Tesseract DLL already exists:
    dir /B "%PROJECT_DIR%\bin\tesseract*.dll"
)

if exist "%PROJECT_DIR%\bin\accelerator_sharedmem.pyd" (
    echo [INFO] accelerator_sharedmem.pyd already exists
    for %%F in ("%PROJECT_DIR%\bin\accelerator_sharedmem.pyd") do echo      Size: %%~zF bytes
)

REM ====================================================================
REM SECTION 9: DISK SPACE CHECK
REM ====================================================================
echo.
echo ============================================================
echo SECTION 9: DISK SPACE VERIFICATION
echo ============================================================
echo.

for /f "tokens=3" %%a in ('dir C:\ /-c ^| findstr /c:"bytes free"') do set FREE_SPACE=%%a
set /a FREE_GB=%FREE_SPACE:~0,-9%
echo Free space on C: drive: %FREE_GB% GB

if %FREE_GB% LSS 10 (
    echo [ERROR] Less than 10GB free space!
    echo        Build requires at least 10GB
    set /a ERRORS+=1
) else if %FREE_GB% LSS 20 (
    echo [WARNING] Less than 20GB free space
    echo          Build may run out of space
    set /a WARNINGS+=1
) else (
    echo [OK] Sufficient disk space
)

REM ====================================================================
REM FINAL SUMMARY
REM ====================================================================
echo.
echo ============================================================
echo VERIFICATION SUMMARY
echo ============================================================
echo.
echo ERRORS:   %ERRORS%
echo WARNINGS: %WARNINGS%
echo.

if %ERRORS% GTR 0 (
    echo ============================================================
    echo CRITICAL: %ERRORS% ERRORS FOUND!
    echo ============================================================
    echo.
    echo The build WILL FAIL unless these errors are fixed:
    echo - Check error messages above
    echo - Fix all [ERROR] items
    echo - Re-run this verification
    echo.
    echo DO NOT PROCEED WITH BUILD!
    echo ============================================================
) else if %WARNINGS% GTR 0 (
    echo ============================================================
    echo CAUTION: %WARNINGS% WARNINGS FOUND
    echo ============================================================
    echo.
    echo The build may succeed but with issues:
    echo - Review [WARNING] messages above
    echo - Some features may not work optimally
    echo.
    echo You can proceed but fixing warnings is recommended.
    echo ============================================================
) else (
    echo ============================================================
    echo PERFECT! ALL CHECKS PASSED!
    echo ============================================================
    echo.
    echo System is ready for the ULTIMATE 9950X3D build!
    echo All prerequisites verified successfully.
    echo.
    echo You can now run: BUILD_ULTIMATE_9950X3D.bat
    echo ============================================================
)

echo.
pause