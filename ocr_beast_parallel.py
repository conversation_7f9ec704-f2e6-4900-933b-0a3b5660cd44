"""
OCR BEAST PARALLEL PROCESSOR
Optimized for AMD Ryzen 9 9950X3D

Key insights:
- Tesseract performs WORSE with OpenMP threads
- Better to run multiple single-threaded processes
- Each process uses ONE thread (OMP_THREAD_LIMIT=1)
- 32 processes for 32 threads on 9950X3D
"""

import os
import multiprocessing as mp
from multiprocessing import Queue, Process
import time
import numpy as np
from typing import Optional, Tuple
import traceback

# CRITICAL: Force single-threaded Tesseract
os.environ['OMP_THREAD_LIMIT'] = '1'
os.environ['OMP_NUM_THREADS'] = '1'

# Import after setting environment
import accelerator_sharedmem


class OCRWorker(Process):
    """Single-threaded OCR worker process"""
    
    def __init__(self, worker_id: int, input_queue: Queue, output_queue: Queue):
        super().__init__()
        self.worker_id = worker_id
        self.input_queue = input_queue
        self.output_queue = output_queue
        self.pipeline = None
        
    def run(self):
        """Run in separate process with single thread"""
        try:
            # Each process gets its own OCR pipeline
            # This is CRITICAL - don't share pipelines between processes
            self.pipeline = accelerator_sharedmem.AcceleratorSharedMemPipeline()
            print(f"[Worker {self.worker_id}] OCR pipeline initialized (single-threaded)")
            
            while True:
                try:
                    # Get work from queue
                    task = self.input_queue.get(timeout=1.0)
                    
                    if task is None:  # Poison pill
                        break
                        
                    task_id, image_data = task
                    
                    # Process OCR (single-threaded, but FAST)
                    start_time = time.perf_counter()
                    result = self.pipeline.process(image_data)
                    elapsed_ms = (time.perf_counter() - start_time) * 1000
                    
                    # Send result back
                    self.output_queue.put({
                        'task_id': task_id,
                        'worker_id': self.worker_id,
                        'result': result,
                        'time_ms': elapsed_ms
                    })
                    
                except mp.queues.Empty:
                    continue
                except Exception as e:
                    print(f"[Worker {self.worker_id}] Error: {e}")
                    traceback.print_exc()
                    
        except Exception as e:
            print(f"[Worker {self.worker_id}] Fatal error: {e}")
            traceback.print_exc()


class OCRBeastProcessor:
    """
    Parallel OCR processor optimized for 9950X3D
    Uses multiple single-threaded processes instead of OpenMP
    """
    
    def __init__(self, num_workers: int = 32):
        """
        Initialize with optimal number of workers
        
        Args:
            num_workers: Number of parallel workers (default 32 for 9950X3D)
        """
        self.num_workers = num_workers
        self.input_queue = mp.Queue(maxsize=num_workers * 2)
        self.output_queue = mp.Queue()
        self.workers = []
        self.task_counter = 0
        
        print(f"Initializing OCR BEAST with {num_workers} workers")
        print("Each worker is SINGLE-THREADED (optimal for Tesseract)")
        
        # Start worker processes
        for i in range(num_workers):
            worker = OCRWorker(i, self.input_queue, self.output_queue)
            worker.start()
            self.workers.append(worker)
            
        print(f"All {num_workers} workers started!")
        
    def process_image(self, image_data: np.ndarray) -> dict:
        """Process single image"""
        task_id = self.task_counter
        self.task_counter += 1
        
        # Send to queue
        self.input_queue.put((task_id, image_data))
        
        # Get result
        result = self.output_queue.get()
        return result
        
    def process_batch(self, images: list) -> list:
        """
        Process batch of images in parallel
        
        This is where the BEAST shines!
        32 images processed simultaneously
        """
        results = {}
        
        # Queue all images
        for i, image in enumerate(images):
            self.input_queue.put((i, image))
            
        # Collect results
        for _ in range(len(images)):
            result = self.output_queue.get()
            results[result['task_id']] = result
            
        # Return in order
        return [results[i] for i in range(len(images))]
        
    def benchmark(self, num_images: int = 100):
        """Benchmark the BEAST"""
        print(f"\nBENCHMARKING {num_images} images...")
        print(f"Using {self.num_workers} parallel workers")
        
        # Create dummy images (1920x1080)
        dummy_image = np.random.randint(0, 255, (1080, 1920, 3), dtype=np.uint8)
        
        start_time = time.perf_counter()
        
        for i in range(num_images):
            self.process_image(dummy_image)
            if (i + 1) % 10 == 0:
                elapsed = time.perf_counter() - start_time
                rate = (i + 1) / elapsed
                print(f"Processed {i+1}/{num_images} - Rate: {rate:.1f} images/sec")
                
        total_time = time.perf_counter() - start_time
        rate = num_images / total_time
        avg_time = (total_time / num_images) * 1000
        
        print(f"\n{'='*50}")
        print(f"BENCHMARK RESULTS:")
        print(f"Total images: {num_images}")
        print(f"Total time: {total_time:.2f} seconds")
        print(f"Average time per image: {avg_time:.1f} ms")
        print(f"Processing rate: {rate:.1f} images/second")
        print(f"{'='*50}")
        
        # Calculate theoretical max with 50ms target
        if avg_time <= 50:
            print(f"✓ TARGET ACHIEVED! Under 50ms per image!")
        else:
            print(f"✗ Current: {avg_time:.1f}ms, Target: 50ms")
            print(f"  Need {avg_time/50:.1f}x speedup")
            
    def shutdown(self):
        """Clean shutdown"""
        print("Shutting down workers...")
        
        # Send poison pills
        for _ in range(self.num_workers):
            self.input_queue.put(None)
            
        # Wait for workers
        for worker in self.workers:
            worker.join(timeout=5.0)
            if worker.is_alive():
                worker.terminate()
                
        print("All workers shut down")


def optimize_for_9950x3d():
    """Set optimal system settings for the BEAST"""
    
    # Windows-specific optimizations
    if os.name == 'nt':
        import subprocess
        
        # Set process priority to high
        try:
            import psutil
            process = psutil.Process()
            process.nice(psutil.HIGH_PRIORITY_CLASS)
            print("Process priority set to HIGH")
        except:
            pass
            
        # Disable CPU throttling (requires admin)
        try:
            subprocess.run('powercfg /setactive 8c5e7fda-e8bf-4a96-9a85-a6e23a8c635c', 
                         shell=True, check=False)  # High performance plan
            print("Power plan set to High Performance")
        except:
            pass
    
    # Set thread affinity for 3D V-Cache CCD
    # The 9950X3D has one CCD with 3D V-Cache
    # We want to prefer that CCD for better cache hits
    try:
        import psutil
        # Prefer first 16 threads (first CCD with 3D V-Cache)
        # Adjust based on your specific 9950X3D configuration
        psutil.Process().cpu_affinity(list(range(16)))
        print("CPU affinity set to 3D V-Cache CCD")
    except:
        pass


if __name__ == "__main__":
    print("="*60)
    print("OCR BEAST PARALLEL PROCESSOR")
    print("Optimized for AMD Ryzen 9 9950X3D")
    print("="*60)
    print()
    
    # Optimize system
    optimize_for_9950x3d()
    
    # Create processor with 32 workers (one per thread)
    processor = OCRBeastProcessor(num_workers=32)
    
    try:
        # Run benchmark
        processor.benchmark(num_images=100)
        
    finally:
        processor.shutdown()
    
    print("\nDone!")