@echo off
REM ====================================================================
REM COMPLETE OCR BUILD FOR AMD RYZEN 9 9950X3D BEAST
REM ====================================================================
REM Builds: OpenCV + Tesseract + Leptonica + Accelerator PYD
REM Includes: ALL image format DLLs
REM Target: AMD Ryzen 9 9950X3D (32 threads, AVX2/FMA)
REM ====================================================================

echo ============================================================
echo COMPLETE OCR BUILD FOR AMD RYZEN 9 9950X3D BEAST
echo ============================================================
echo.
echo This will build EVERYTHING from scratch:
echo 1. OpenCV 4.10.0 with opencv_world
echo 2. Leptonica 1.85.0 
echo 3. Tesseract 5.5.1
echo 4. accelerator_sharedmem.pyd
echo 5. All image format DLLs
echo.
echo BUILD ENV: Single-core VM (very slow)
echo TARGET: AMD Ryzen 9 9950X3D (32 threads)
echo.
echo ESTIMATED TIME: 4-6 HOURS TOTAL
echo.
pause

set OPENCV_VERSION=4.10.0
set TESSERACT_VERSION=5.5.1
set LEPTONICA_VERSION=1.85.0
set BUILD_DIR=C:\TANK\ocr_accelerator\source
set INSTALL_DIR=C:\TANK\ocr_accelerator\deps
set VCPKG_BIN=C:\tools\vcpkg\installed\x64-windows\bin
set PYTHON_HOME=C:\Python311

REM ====================================================================
REM PART 1: BUILD OPENCV
REM ====================================================================
echo.
echo ============================================================
echo PART 1 of 5: BUILDING OPENCV %OPENCV_VERSION%
echo ============================================================
echo.
echo This builds opencv_world DLL (single file, ~55MB)
echo Estimated time: 2-3 hours
echo.
pause

cd /d %BUILD_DIR%

if exist opencv\CMakeLists.txt (
    echo OpenCV source already exists
    choice /C YN /T 10 /D N /M "Skip OpenCV build"
    if errorlevel 2 goto PART2_LEPTONICA
)

echo Downloading OpenCV %OPENCV_VERSION%...
curl -L -o opencv.zip https://github.com/opencv/opencv/archive/refs/tags/%OPENCV_VERSION%.zip
if %ERRORLEVEL% NEQ 0 (
    echo ERROR: Failed to download OpenCV!
    pause
    exit /b 1
)

echo Extracting OpenCV...
tar -xf opencv.zip
if exist opencv-%OPENCV_VERSION% (
    move opencv-%OPENCV_VERSION% opencv
) else (
    echo ERROR: Extraction failed!
    pause
    exit /b 1
)
del opencv.zip

echo Downloading OpenCV contrib modules...
curl -L -o opencv_contrib.zip https://github.com/opencv/opencv_contrib/archive/refs/tags/%OPENCV_VERSION%.zip
tar -xf opencv_contrib.zip
if exist opencv_contrib-%OPENCV_VERSION% (
    move opencv_contrib-%OPENCV_VERSION% opencv_contrib
) else (
    echo ERROR: Contrib extraction failed!
    pause
    exit /b 1
)
del opencv_contrib.zip

cd opencv
if exist build rmdir /s /q build
mkdir build
cd build

echo.
echo Configuring OpenCV for 9950X3D...
cmake .. -G "Visual Studio 17 2022" -A x64 ^
    -DCMAKE_BUILD_TYPE=Release ^
    -DCMAKE_INSTALL_PREFIX=%INSTALL_DIR%\opencv ^
    -DBUILD_opencv_world=ON ^
    -DBUILD_SHARED_LIBS=ON ^
    -DWITH_OPENMP=ON ^
    -DWITH_IPP=OFF ^
    -DCPU_BASELINE=SSE4_2 ^
    -DCPU_DISPATCH=SSE4_2,AVX,AVX2,FMA3 ^
    -DENABLE_FAST_MATH=ON ^
    -DBUILD_TESTS=OFF ^
    -DBUILD_PERF_TESTS=OFF ^
    -DBUILD_EXAMPLES=OFF ^
    -DBUILD_DOCS=OFF ^
    -DBUILD_opencv_apps=OFF ^
    -DBUILD_opencv_python2=OFF ^
    -DBUILD_opencv_python3=OFF ^
    -DOPENCV_EXTRA_MODULES_PATH=../../opencv_contrib/modules ^
    -DWITH_CUDA=OFF ^
    -DWITH_OPENCL=OFF ^
    -DENABLE_PRECOMPILED_HEADERS=OFF

if %ERRORLEVEL% NEQ 0 (
    echo ERROR: OpenCV CMake configuration failed!
    pause
    exit /b 1
)

echo.
echo Building OpenCV (single thread)...
echo Started at: %time%
cmake --build . --config Release --target INSTALL -- /m:1

if %ERRORLEVEL% NEQ 0 (
    echo ERROR: OpenCV build failed!
    pause
    exit /b 1
)

echo Completed at: %time%
echo [OK] OpenCV built successfully!

cd /d C:\TANK\ocr_accelerator
xcopy /Y "%INSTALL_DIR%\opencv\x64\vc17\bin\*.dll" "bin\" >nul 2>&1
xcopy /Y "%INSTALL_DIR%\opencv\x64\vc17\lib\*.lib" "deps\lib\" >nul 2>&1
xcopy /E /Y "%INSTALL_DIR%\opencv\include\*" "deps\include\" >nul 2>&1

echo.
echo OpenCV installation complete. Checking...
if exist bin\opencv_world*.dll (
    dir /B bin\opencv_world*.dll
    echo [SUCCESS] OpenCV world DLL installed
) else (
    echo [WARNING] opencv_world DLL not found
)

echo.
echo ============================================================
echo PART 1 COMPLETE - OpenCV built
echo ============================================================
pause

:PART2_LEPTONICA
REM ====================================================================
REM PART 2: BUILD LEPTONICA
REM ====================================================================
echo.
echo ============================================================
echo PART 2 of 5: BUILDING LEPTONICA %LEPTONICA_VERSION%
echo ============================================================
echo.
echo Leptonica is required by Tesseract for image processing
echo Estimated time: 30-45 minutes
echo.
pause

cd /d %BUILD_DIR%

if exist leptonica rmdir /s /q leptonica

echo Downloading Leptonica %LEPTONICA_VERSION%...
curl -L -o leptonica.zip https://github.com/DanBloomberg/leptonica/archive/refs/tags/%LEPTONICA_VERSION%.zip
if %ERRORLEVEL% NEQ 0 (
    echo ERROR: Failed to download Leptonica!
    pause
    exit /b 1
)

echo Extracting Leptonica...
tar -xf leptonica.zip
move leptonica-%LEPTONICA_VERSION% leptonica
del leptonica.zip

cd leptonica
mkdir build
cd build

echo.
echo Configuring Leptonica for 9950X3D...
cmake .. -G "Visual Studio 17 2022" -A x64 ^
    -DCMAKE_BUILD_TYPE=Release ^
    -DCMAKE_INSTALL_PREFIX=%INSTALL_DIR%\leptonica ^
    -DBUILD_SHARED_LIBS=ON ^
    -DSW_BUILD=OFF ^
    -DCMAKE_CXX_FLAGS_RELEASE="/O2 /Ob2 /Oi /Ot /Oy /GT /GL /GS- /fp:fast /arch:AVX2" ^
    -DCMAKE_C_FLAGS_RELEASE="/O2 /Ob2 /Oi /Ot /Oy /GT /GL /GS- /fp:fast /arch:AVX2"

if %ERRORLEVEL% NEQ 0 (
    echo ERROR: Leptonica CMake configuration failed!
    pause
    exit /b 1
)

echo.
echo Building Leptonica (single thread)...
echo Started at: %time%
cmake --build . --config Release --target INSTALL -- /m:1

if %ERRORLEVEL% NEQ 0 (
    echo ERROR: Leptonica build failed!
    pause
    exit /b 1
)

echo Completed at: %time%
echo [OK] Leptonica built successfully!

REM Create CMake config for Tesseract to find
if not exist "%INSTALL_DIR%\leptonica\lib\cmake\leptonica" (
    mkdir "%INSTALL_DIR%\leptonica\lib\cmake\leptonica"
)

(
echo # Leptonica CMake Config
echo set(Leptonica_INCLUDE_DIRS "%INSTALL_DIR%\leptonica\include"^)
echo set(Leptonica_LIBRARIES "%INSTALL_DIR%\leptonica\lib\leptonica-1.85.0.lib"^)
echo set(Leptonica_FOUND TRUE^)
) > "%INSTALL_DIR%\leptonica\lib\cmake\leptonica\LeptonicaConfig.cmake"

echo.
echo ============================================================
echo PART 2 COMPLETE - Leptonica built
echo ============================================================
pause

REM ====================================================================
REM PART 3: BUILD TESSERACT
REM ====================================================================
echo.
echo ============================================================
echo PART 3 of 5: BUILDING TESSERACT %TESSERACT_VERSION%
echo ============================================================
echo.
echo Tesseract OCR engine with LSTM and Legacy engines
echo Estimated time: 45-60 minutes
echo.
pause

cd /d %BUILD_DIR%

if exist tesseract rmdir /s /q tesseract

echo Downloading Tesseract %TESSERACT_VERSION%...
curl -L -o tesseract.zip https://github.com/tesseract-ocr/tesseract/archive/refs/tags/%TESSERACT_VERSION%.zip
if %ERRORLEVEL% NEQ 0 (
    echo ERROR: Failed to download Tesseract!
    pause
    exit /b 1
)

echo Extracting Tesseract...
tar -xf tesseract.zip
move tesseract-%TESSERACT_VERSION% tesseract
del tesseract.zip

cd tesseract
mkdir build
cd build

echo.
echo Configuring Tesseract for 9950X3D...
cmake .. -G "Visual Studio 17 2022" -A x64 ^
    -DCMAKE_BUILD_TYPE=Release ^
    -DCMAKE_INSTALL_PREFIX=%INSTALL_DIR%\tesseract ^
    -DBUILD_SHARED_LIBS=ON ^
    -DLeptonica_DIR=%INSTALL_DIR%\leptonica\lib\cmake\leptonica ^
    -DCMAKE_PREFIX_PATH=%INSTALL_DIR%\leptonica ^
    -DSW_BUILD=OFF ^
    -DBUILD_TRAINING_TOOLS=OFF ^
    -DBUILD_TESTS=OFF ^
    -DDISABLED_LEGACY_ENGINE=OFF ^
    -DOPENMP_BUILD=ON ^
    -DCMAKE_CXX_FLAGS_RELEASE="/O2 /Ob2 /Oi /Ot /Oy /GT /GL /GS- /fp:fast /arch:AVX2 /openmp" ^
    -DCMAKE_C_FLAGS_RELEASE="/O2 /Ob2 /Oi /Ot /Oy /GT /GL /GS- /fp:fast /arch:AVX2 /openmp"

if %ERRORLEVEL% NEQ 0 (
    echo ERROR: Tesseract CMake configuration failed!
    echo Trying with explicit Leptonica paths...
    cmake .. -G "Visual Studio 17 2022" -A x64 ^
        -DCMAKE_BUILD_TYPE=Release ^
        -DCMAKE_INSTALL_PREFIX=%INSTALL_DIR%\tesseract ^
        -DBUILD_SHARED_LIBS=ON ^
        -DLeptonica_INCLUDE_DIR=%INSTALL_DIR%\leptonica\include ^
        -DLeptonica_LIBRARY=%INSTALL_DIR%\leptonica\lib\leptonica-1.85.0.lib ^
        -DSW_BUILD=OFF ^
        -DBUILD_TRAINING_TOOLS=OFF ^
        -DBUILD_TESTS=OFF
    
    if %ERRORLEVEL% NEQ 0 (
        echo ERROR: Tesseract configuration failed completely!
        pause
        exit /b 1
    )
)

echo.
echo Building Tesseract (single thread)...
echo Started at: %time%
cmake --build . --config Release --target INSTALL -- /m:1

if %ERRORLEVEL% NEQ 0 (
    echo ERROR: Tesseract build failed!
    pause
    exit /b 1
)

echo Completed at: %time%
echo [OK] Tesseract built successfully!

echo.
echo Downloading OCR language data...
cd /d %INSTALL_DIR%\tesseract\bin
if not exist tessdata mkdir tessdata
curl -L -o tessdata\eng.traineddata https://github.com/tesseract-ocr/tessdata_best/raw/main/eng.traineddata
curl -L -o tessdata\osd.traineddata https://github.com/tesseract-ocr/tessdata_best/raw/main/osd.traineddata

echo.
echo ============================================================
echo PART 3 COMPLETE - Tesseract built
echo ============================================================
pause

REM ====================================================================
REM PART 4: COPY ALL DLLS INCLUDING IMAGE FORMATS
REM ====================================================================
echo.
echo ============================================================
echo PART 4 of 5: INSTALLING ALL DLLS
echo ============================================================
echo.
echo This copies all DLLs including image format libraries
echo.
pause

cd /d C:\TANK\ocr_accelerator

echo Copying OpenCV DLLs...
xcopy /Y "%INSTALL_DIR%\opencv\x64\vc17\bin\*.dll" "bin\" >nul 2>&1

echo Copying Leptonica DLLs...
xcopy /Y "%INSTALL_DIR%\leptonica\bin\*.dll" "bin\" >nul 2>&1

echo Copying Tesseract DLLs...
xcopy /Y "%INSTALL_DIR%\tesseract\bin\*.dll" "bin\" >nul 2>&1

echo Copying libraries...
xcopy /Y "%INSTALL_DIR%\opencv\x64\vc17\lib\*.lib" "deps\lib\" >nul 2>&1
xcopy /Y "%INSTALL_DIR%\leptonica\lib\*.lib" "deps\lib\" >nul 2>&1
xcopy /Y "%INSTALL_DIR%\tesseract\lib\*.lib" "deps\lib\" >nul 2>&1

echo Copying headers...
xcopy /E /Y "%INSTALL_DIR%\opencv\include\*" "deps\include\" >nul 2>&1
xcopy /E /Y "%INSTALL_DIR%\leptonica\include\*" "deps\include\" >nul 2>&1
xcopy /E /Y "%INSTALL_DIR%\tesseract\include\*" "deps\include\" >nul 2>&1

echo Copying tessdata...
xcopy /E /Y "%INSTALL_DIR%\tesseract\bin\tessdata\*" "bin\tessdata\" >nul 2>&1

echo.
echo Copying IMAGE FORMAT DLLs from vcpkg...
cd bin

set IMAGE_DLLS=libpng16.dll jpeg62.dll turbojpeg.dll tiff.dll gif.dll openjp2.dll libwebp.dll libwebpdecoder.dll libwebpdemux.dll libwebpmux.dll zlib1.dll liblzma.dll bz2.dll libsharpyuv.dll archive.dll lz4.dll zstd.dll libcrypto-3-x64.dll libssl-3-x64.dll libcurl.dll sqlite3.dll libexpat.dll

for %%D in (%IMAGE_DLLS%) do (
    if exist "%VCPKG_BIN%\%%D" (
        copy /Y "%VCPKG_BIN%\%%D" . >nul
        echo [COPIED] %%D
    ) else (
        echo [SKIP] %%D not in vcpkg
    )
)

REM Try alternate names
xcopy /Y "%VCPKG_BIN%\png*.dll" . >nul 2>&1
xcopy /Y "%VCPKG_BIN%\jpeg*.dll" . >nul 2>&1
xcopy /Y "%VCPKG_BIN%\tiff*.dll" . >nul 2>&1
xcopy /Y "%VCPKG_BIN%\zlib*.dll" . >nul 2>&1

echo.
echo Verifying critical DLLs...
if exist opencv_world*.dll echo [OK] OpenCV world DLL present
if exist tesseract*.dll echo [OK] Tesseract DLL present
if exist leptonica*.dll echo [OK] Leptonica DLL present
if exist lept*.dll echo [OK] Leptonica DLL present (alternate name)

echo.
echo ============================================================
echo PART 4 COMPLETE - All DLLs installed
echo ============================================================
pause

REM ====================================================================
REM PART 5: BUILD ACCELERATOR_SHAREDMEM.PYD
REM ====================================================================
echo.
echo ============================================================
echo PART 5 of 5: BUILDING ACCELERATOR_SHAREDMEM.PYD
echo ============================================================
echo.
echo This builds the final Python module
echo.
pause

cd /d C:\TANK\ocr_accelerator

if not exist accelerator_sharedmem.cpp (
    echo ERROR: accelerator_sharedmem.cpp not found!
    echo Please copy the source file to C:\TANK\ocr_accelerator\
    pause
    exit /b 1
)

echo Installing Python dependencies...
%PYTHON_HOME%\python.exe -m pip install pybind11 numpy >nul 2>&1

echo Getting Python paths...
for /f "delims=" %%i in ('%PYTHON_HOME%\python.exe -c "import sysconfig; print(sysconfig.get_path('include'))"') do set PYTHON_INCLUDE=%%i
for /f "delims=" %%i in ('%PYTHON_HOME%\python.exe -c "import pybind11; print(pybind11.get_include())"') do set PYBIND11_INCLUDE=%%i
for /f "delims=" %%i in ('%PYTHON_HOME%\python.exe -c "import numpy; print(numpy.get_include())"') do set NUMPY_INCLUDE=%%i

echo.
echo Compiling accelerator_sharedmem.cpp...
cl /MD /O2 /Ob2 /Oi /Ot /Oy /GT /GL /GS- /fp:fast /arch:AVX2 ^
    /I"%PYTHON_INCLUDE%" ^
    /I"%PYBIND11_INCLUDE%" ^
    /I"%NUMPY_INCLUDE%" ^
    /I"deps\include" ^
    /I"deps\opencv\include" ^
    /I"deps\tesseract\include" ^
    /I"deps\leptonica\include" ^
    /D_CRT_SECURE_NO_WARNINGS ^
    /DNDEBUG ^
    /c accelerator_sharedmem.cpp

if %ERRORLEVEL% NEQ 0 (
    echo ERROR: Compilation failed!
    pause
    exit /b 1
)

echo Linking to create .pyd...
link /DLL /OUT:bin\accelerator_sharedmem.pyd ^
    /LIBPATH:"%PYTHON_HOME%\libs" ^
    /LIBPATH:"deps\lib" ^
    /LIBPATH:"deps\opencv\x64\vc17\lib" ^
    /LIBPATH:"deps\tesseract\lib" ^
    /LIBPATH:"deps\leptonica\lib" ^
    accelerator_sharedmem.obj ^
    python311.lib ^
    opencv_world*.lib ^
    tesseract*.lib ^
    leptonica*.lib ^
    /LTCG

if %ERRORLEVEL% NEQ 0 (
    echo ERROR: Linking failed!
    pause
    exit /b 1
)

del accelerator_sharedmem.obj
echo [OK] accelerator_sharedmem.pyd built!

echo.
echo ============================================================
echo PART 5 COMPLETE - PYD module built
echo ============================================================
pause

REM ====================================================================
REM FINAL TEST
REM ====================================================================
echo.
echo ============================================================
echo FINAL TEST
echo ============================================================
echo.

cd bin
%PYTHON_HOME%\python.exe -c "import accelerator_sharedmem; print('[SUCCESS] Module loads correctly!')" 2>&1

if %ERRORLEVEL% NEQ 0 (
    echo.
    echo [FAILED] Module won't load!
    echo.
    echo Checking with detailed error...
    %PYTHON_HOME%\python.exe -c "import accelerator_sharedmem"
    echo.
    echo DLLs present:
    dir *.dll /B | findstr /i "opencv tesseract leptonica"
    echo.
    echo Image DLLs present:
    dir *.dll /B | findstr /i "png jpeg tiff webp gif"
) else (
    echo.
    echo ============================================================
    echo COMPLETE SUCCESS!
    echo ============================================================
    echo.
    echo All components built and working:
    echo - OpenCV %OPENCV_VERSION% (opencv_world)
    echo - Leptonica %LEPTONICA_VERSION%
    echo - Tesseract %TESSERACT_VERSION%
    echo - accelerator_sharedmem.pyd
    echo - All image format DLLs
    echo.
    echo Optimized for AMD Ryzen 9 9950X3D:
    echo - AVX2 + FMA instructions
    echo - OpenMP (32 threads)
    echo - 3D V-Cache optimized
    echo.
    echo Ready for deployment to BEAST!
)

echo.
pause