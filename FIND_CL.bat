@echo off
echo ============================================================
echo SEARCHING FOR CL.EXE ON SYSTEM
echo ============================================================
echo.

echo Checking PATH for cl.exe...
where cl.exe 2>nul
if %ERRORLEVEL% EQU 0 (
    echo.
    echo FOUND cl.exe in PATH!
) else (
    echo cl.exe NOT in PATH
)

echo.
echo Searching common Visual Studio locations...
echo.

REM VS 2022 Community
if exist "C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\" (
    echo Found VS2022 Community installation
    dir /s /b "C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\*cl.exe" 2>nul
)

REM VS 2022 Professional  
if exist "C:\Program Files\Microsoft Visual Studio\2022\Professional\VC\Tools\MSVC\" (
    echo Found VS2022 Professional installation
    dir /s /b "C:\Program Files\Microsoft Visual Studio\2022\Professional\VC\Tools\MSVC\*cl.exe" 2>nul
)

REM VS 2022 Enterprise
if exist "C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\" (
    echo Found VS2022 Enterprise installation
    dir /s /b "C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\*cl.exe" 2>nul
)

REM VS 2019
if exist "C:\Program Files (x86)\Microsoft Visual Studio\2019\" (
    echo Found VS2019 installation
    dir /s /b "C:\Program Files (x86)\Microsoft Visual Studio\2019\*cl.exe" 2>nul
)

echo.
echo ============================================================
echo CHECKING ENVIRONMENT TYPE
echo ============================================================
echo.

echo Are we in a regular Command Prompt or VS Developer Prompt?
echo.

REM Just check if ANY VS-related variable exists
set VS_FOUND=NO

if defined VS140COMNTOOLS set VS_FOUND=YES
if defined VS150COMNTOOLS set VS_FOUND=YES  
if defined VS160COMNTOOLS set VS_FOUND=YES
if defined VS170COMNTOOLS set VS_FOUND=YES
if defined INCLUDE set VS_FOUND=MAYBE
if defined LIB set VS_FOUND=MAYBE
if defined LIBPATH set VS_FOUND=MAYBE

echo VS-related environment: %VS_FOUND%

echo.
echo Checking INCLUDE path:
if defined INCLUDE (
    echo INCLUDE is defined
    echo %INCLUDE% | findstr /C:"MSVC" >nul
    if %ERRORLEVEL% EQU 0 (
        echo [DETECTED] MSVC paths in INCLUDE - likely in VS prompt
    )
) else (
    echo INCLUDE not defined
)

echo.
echo Checking LIB path:
if defined LIB (
    echo LIB is defined
    echo %LIB% | findstr /C:"MSVC" >nul
    if %ERRORLEVEL% EQU 0 (
        echo [DETECTED] MSVC paths in LIB - likely in VS prompt
    )
) else (
    echo LIB not defined
)

echo.
echo ============================================================
echo MANUAL SETUP COMMANDS
echo ============================================================
echo.
echo If cl.exe was found above but not in PATH, you can:
echo.
echo Option 1: Use VS Developer Command Prompt
echo   Start Menu -> "x64 Native Tools Command Prompt for VS 2022"
echo.
echo Option 2: Run vcvarsall.bat manually
echo   "C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Auxiliary\Build\vcvarsall.bat" x64
echo.
pause