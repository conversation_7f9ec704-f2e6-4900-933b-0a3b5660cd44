@echo off
REM ====================================================================
REM BUILD VERIFICATION - WORKING VERSION FOR VS BUILD TOOLS
REM ====================================================================

echo ============================================================
echo BUILD VERIFICATION FOR VS BUILD TOOLS
echo ============================================================
echo.

set READY=YES
set VS_PATH=C:\Program Files (x86)\Microsoft Visual Studio\2022\BuildTools

REM ====================================================================
REM SETUP VS ENVIRONMENT IF NEEDED
REM ====================================================================
echo Checking Visual Studio Build Tools environment...

where cl.exe >nul 2>&1
if %ERRORLEVEL% NEQ 0 (
    echo cl.exe not in PATH, setting up environment...
    
    if exist "%VS_PATH%\VC\Auxiliary\Build\vcvarsall.bat" (
        echo Found vcvarsall.bat, initializing x64 build environment...
        call "%VS_PATH%\VC\Auxiliary\Build\vcvarsall.bat" x64
        echo Environment initialized!
    ) else (
        echo ERROR: Cannot find vcvarsall.bat
        echo Expected at: %VS_PATH%\VC\Auxiliary\Build\vcvarsall.bat
        set READY=NO
    )
) else (
    echo cl.exe already in PATH
)

REM Verify cl.exe is now available
where cl.exe >nul 2>&1
if %ERRORLEVEL% EQU 0 (
    echo [OK] C++ compiler ready
    for /f "tokens=*" %%i in ('where cl.exe') do echo      Location: %%i
) else (
    echo [ERROR] cl.exe still not found after setup!
    set READY=NO
)
echo.

REM ====================================================================
REM CHECK OTHER TOOLS
REM ====================================================================
echo Checking other required tools...

REM Check CMake
where cmake.exe >nul 2>&1
if %ERRORLEVEL% EQU 0 (
    echo [OK] CMake found
) else (
    echo [ERROR] CMake not found - install from cmake.org
    set READY=NO
)

REM Check Python
set PYTHON_OK=NO
if exist "C:\Python311\python.exe" (
    for /f "tokens=2" %%i in ('C:\Python311\python.exe --version 2^>^&1') do set PY_VER=%%i
    echo [OK] Python found at C:\Python311 (version %PY_VER%)
    set PYTHON_OK=YES
    set PYTHON_PATH=C:\Python311\python.exe
) else (
    where python.exe >nul 2>&1
    if %ERRORLEVEL% EQU 0 (
        for /f "tokens=2" %%i in ('python --version 2^>^&1') do set PY_VER=%%i
        echo [OK] Python found in PATH (version %PY_VER%)
        set PYTHON_OK=YES
        set PYTHON_PATH=python
    ) else (
        echo [ERROR] Python not found
        set READY=NO
    )
)

REM Check curl and tar
where curl.exe >nul 2>&1
if %ERRORLEVEL% EQU 0 (
    echo [OK] curl found
) else (
    echo [ERROR] curl not found
    set READY=NO
)

where tar.exe >nul 2>&1
if %ERRORLEVEL% EQU 0 (
    echo [OK] tar found
) else (
    echo [ERROR] tar not found
    set READY=NO
)
echo.

REM ====================================================================
REM CREATE DIRECTORIES
REM ====================================================================
echo Setting up project directories...
if not exist "C:\TANK\ocr_accelerator" mkdir "C:\TANK\ocr_accelerator"
if not exist "C:\TANK\ocr_accelerator\source" mkdir "C:\TANK\ocr_accelerator\source"
if not exist "C:\TANK\ocr_accelerator\deps" mkdir "C:\TANK\ocr_accelerator\deps"
if not exist "C:\TANK\ocr_accelerator\bin" mkdir "C:\TANK\ocr_accelerator\bin"
if not exist "C:\TANK\ocr_accelerator\bin\tessdata" mkdir "C:\TANK\ocr_accelerator\bin\tessdata"
echo [OK] Directories ready
echo.

REM ====================================================================
REM CHECK SOURCE FILE
REM ====================================================================
echo Checking source files...
if exist "C:\TANK\ocr_accelerator\accelerator_sharedmem.cpp" (
    echo [OK] accelerator_sharedmem.cpp found
) else (
    echo [WARNING] accelerator_sharedmem.cpp not found
    echo          Copy from backup or create new
)
echo.

REM ====================================================================
REM TEST COMPILER FLAGS
REM ====================================================================
echo Testing compiler optimization flags...

echo int main() { return 0; } > test.cpp

cl.exe /c /O2 test.cpp >nul 2>&1
if %ERRORLEVEL% EQU 0 (
    echo [OK] Basic optimizations work
) else (
    echo [WARNING] Compiler test failed
)

cl.exe /c /arch:AVX2 test.cpp >nul 2>&1
if %ERRORLEVEL% EQU 0 (
    echo [OK] AVX2 supported
) else (
    echo [INFO] AVX2 not supported
)

cl.exe /c /arch:AVX512 test.cpp >nul 2>&1
if %ERRORLEVEL% EQU 0 (
    echo [OK] AVX512 supported
) else (
    echo [INFO] AVX512 not supported, will use AVX2
)

del test.cpp >nul 2>&1
del test.obj >nul 2>&1
echo.

REM ====================================================================
REM INSTALL PYTHON PACKAGES IF NEEDED
REM ====================================================================
if "%PYTHON_OK%"=="YES" (
    echo Checking Python packages...
    
    %PYTHON_PATH% -c "import pybind11" >nul 2>&1
    if %ERRORLEVEL% NEQ 0 (
        echo Installing pybind11...
        %PYTHON_PATH% -m pip install pybind11
    ) else (
        echo [OK] pybind11 installed
    )
    
    %PYTHON_PATH% -c "import numpy" >nul 2>&1
    if %ERRORLEVEL% NEQ 0 (
        echo Installing numpy...
        %PYTHON_PATH% -m pip install numpy
    ) else (
        echo [OK] numpy installed
    )
)
echo.

REM ====================================================================
REM SUMMARY
REM ====================================================================
echo ============================================================
if "%READY%"=="YES" (
    echo STATUS: READY TO BUILD!
    echo ============================================================
    echo.
    echo Build environment is configured and ready.
    echo.
    echo Visual Studio Build Tools: %VS_PATH%
    echo.
    echo Next step: Run your build script
) else (
    echo STATUS: NOT READY
    echo ============================================================
    echo.
    echo Fix the [ERROR] items above before building.
)
echo ============================================================
echo.
pause