project(kleidicv_hal)

set(KLEIDICV_SOURCE_PATH "" CACHE PATH "Directory containing KleidiCV sources")
ocv_update(K<PERSON><PERSON><PERSON>V_SRC_COMMIT "0.1.0")
ocv_update(<PERSON><PERSON>IDICV_SRC_HASH "9388f28cf2fbe3338197b2b57d491468")

if(KLEIDICV_SOURCE_PATH)
  set(THE_ROOT "${KLEIDICV_SOURCE_PATH}")
else()
  ocv_download(FILENAME "kleidicv-${KLEIDICV_SRC_COMMIT}.tar.gz"
                HASH ${KLEIDICV_SRC_HASH}
                URL
                  "${OPENCV_KLEIDICV_URL}"
                  "$ENV{OPENCV_KLEIDICV_URL}"
                  "https://gitlab.arm.com/kleidi/kleidicv/-/archive/${KLEIDICV_SRC_COMMIT}/"
                DESTINATION_DIR "${OpenCV_BINARY_DIR}/3rdparty/kleidicv/"
                ID KLEIDICV
                STATUS res
                UNPACK RELATIVE_URL)
  set(THE_ROOT "${OpenCV_BINARY_DIR}/3rdparty/kleidicv/kleidicv-${KLEIDICV_SRC_COMMIT}")
endif()

include("${THE_ROOT}/adapters/opencv/CMakeLists.txt")
