@echo off
REM ====================================================================
REM COMPLETE OCR BUILD FOR AMD RYZEN 9 9950X3D BEAST - FINAL VERSION
REM ====================================================================
REM CORRECTED: All paths, tessdata location, compiler flags
REM ====================================================================

echo ============================================================
echo COMPLETE OCR BUILD - FINAL CORRECTED VERSION
echo ============================================================
echo.
echo This builds EVERYTHING with correct paths:
echo 1. OpenCV 4.10.0 (opencv_world DLL)
echo 2. Leptonica 1.85.0 
echo 3. Tesseract 5.5.1
echo 4. accelerator_sharedmem.pyd
echo 5. All image format DLLs
echo 6. tessdata in correct location
echo.
echo BUILD ENV: Single-core VM
echo TARGET: AMD Ryzen 9 9950X3D (32 threads)
echo.
echo IMPORTANT: tessdata will be in bin\tessdata
echo.
pause

set OPENCV_VERSION=4.10.0
set TESSERACT_VERSION=5.5.1
set LEPTONICA_VERSION=1.85.0
set BUILD_DIR=C:\TANK\ocr_accelerator\source
set INSTALL_DIR=C:\TANK\ocr_accelerator\deps
set PROJECT_DIR=C:\TANK\ocr_accelerator
set ARTIFACTS_DIR=C:\TANK\ocr_accelerator\build_artifacts
set VCPKG_BIN=C:\tools\vcpkg\installed\x64-windows\bin
set PYTHON_HOME=C:\Python311

REM Create directories
if not exist %BUILD_DIR% mkdir %BUILD_DIR%
if not exist %INSTALL_DIR% mkdir %INSTALL_DIR%
if not exist %ARTIFACTS_DIR% mkdir %ARTIFACTS_DIR%
if not exist %ARTIFACTS_DIR%\logs mkdir %ARTIFACTS_DIR%\logs
if not exist %PROJECT_DIR%\bin mkdir %PROJECT_DIR%\bin
if not exist %PROJECT_DIR%\deps\lib mkdir %PROJECT_DIR%\deps\lib
if not exist %PROJECT_DIR%\deps\include mkdir %PROJECT_DIR%\deps\include

REM ====================================================================
REM PART 1: BUILD OPENCV WITH CORRECT FLAGS
REM ====================================================================
echo.
echo ============================================================
echo PART 1 of 6: BUILDING OPENCV %OPENCV_VERSION%
echo ============================================================
echo.
echo Building opencv_world DLL with AVX2/FMA (NO AVX512 flag)
echo Estimated time: 2-3 hours on single core
echo.
pause

cd /d %BUILD_DIR%

if exist opencv\build\OpenCV.sln (
    echo OpenCV.sln already exists
    choice /C YN /T 10 /D N /M "Skip OpenCV build"
    if errorlevel 2 goto PART2_LEPTONICA
)

if not exist opencv\CMakeLists.txt (
    echo Downloading OpenCV %OPENCV_VERSION%...
    curl -L -o opencv.zip https://github.com/opencv/opencv/archive/refs/tags/%OPENCV_VERSION%.zip
    if %ERRORLEVEL% NEQ 0 (
        echo ERROR: Failed to download OpenCV!
        pause
        exit /b 1
    )

    echo Extracting OpenCV...
    tar -xf opencv.zip
    if exist opencv-%OPENCV_VERSION% (
        move opencv-%OPENCV_VERSION% opencv
    ) else (
        echo ERROR: Extraction failed!
        pause
        exit /b 1
    )
    del opencv.zip

    echo Downloading OpenCV contrib...
    curl -L -o opencv_contrib.zip https://github.com/opencv/opencv_contrib/archive/refs/tags/%OPENCV_VERSION%.zip
    tar -xf opencv_contrib.zip
    if exist opencv_contrib-%OPENCV_VERSION% (
        move opencv_contrib-%OPENCV_VERSION% opencv_contrib
    ) else (
        echo ERROR: Contrib extraction failed!
        pause
        exit /b 1
    )
    del opencv_contrib.zip
)

cd opencv
if not exist build mkdir build
cd build

echo.
echo Configuring OpenCV for 9950X3D (using CPU_DISPATCH, not /arch flag)...
cmake .. -G "Visual Studio 17 2022" -A x64 ^
    -DCMAKE_BUILD_TYPE=Release ^
    -DCMAKE_INSTALL_PREFIX=%INSTALL_DIR%\opencv ^
    -DBUILD_opencv_world=ON ^
    -DBUILD_SHARED_LIBS=ON ^
    -DWITH_TBB=OFF ^
    -DWITH_OPENMP=ON ^
    -DWITH_IPP=OFF ^
    -DCPU_BASELINE=SSE4_2 ^
    -DCPU_DISPATCH=SSE4_2,AVX,AVX2,FMA3,AVX_512F,AVX512_COMMON,AVX512_SKX ^
    -DENABLE_FAST_MATH=ON ^
    -DBUILD_TESTS=OFF ^
    -DBUILD_PERF_TESTS=OFF ^
    -DBUILD_EXAMPLES=OFF ^
    -DBUILD_DOCS=OFF ^
    -DBUILD_opencv_apps=OFF ^
    -DBUILD_opencv_python2=OFF ^
    -DBUILD_opencv_python3=OFF ^
    -DOPENCV_EXTRA_MODULES_PATH=../../opencv_contrib/modules ^
    -DWITH_CUDA=OFF ^
    -DWITH_OPENCL=OFF ^
    -DWITH_PNG=ON ^
    -DWITH_JPEG=ON ^
    -DWITH_TIFF=ON ^
    -DWITH_WEBP=ON ^
    -DENABLE_PRECOMPILED_HEADERS=OFF

if %ERRORLEVEL% NEQ 0 (
    echo ERROR: OpenCV CMake configuration failed!
    pause
    exit /b 1
)

echo [OK] OpenCV configured successfully
if exist OpenCV.sln copy OpenCV.sln %ARTIFACTS_DIR%\

echo.
echo Building OpenCV with SINGLE thread (VM limitation)...
echo Started at: %time%
cmake --build . --config Release --target INSTALL -- /m:1

if %ERRORLEVEL% NEQ 0 (
    echo ERROR: OpenCV build failed!
    pause
    exit /b 1
)

echo Completed at: %time%
echo [OK] OpenCV built!

echo Installing OpenCV files...
cd /d %PROJECT_DIR%
xcopy /Y "%INSTALL_DIR%\opencv\x64\vc17\bin\*.dll" "bin\" >nul 2>&1
xcopy /Y "%INSTALL_DIR%\opencv\x64\vc17\lib\*.lib" "deps\lib\" >nul 2>&1
xcopy /E /Y "%INSTALL_DIR%\opencv\include\*" "deps\include\" >nul 2>&1

if exist bin\opencv_world*.dll (
    dir /B bin\opencv_world*.dll
    echo [SUCCESS] OpenCV world DLL installed
) else (
    echo [ERROR] opencv_world DLL not found!
)

echo.
echo ============================================================
echo PART 1 COMPLETE - OpenCV built
echo ============================================================
pause

:PART2_LEPTONICA
REM ====================================================================
REM PART 2: BUILD LEPTONICA
REM ====================================================================
echo.
echo ============================================================
echo PART 2 of 6: BUILDING LEPTONICA %LEPTONICA_VERSION%
echo ============================================================
echo.
echo Required by Tesseract for image processing
echo.
pause

cd /d %BUILD_DIR%

if exist leptonica rmdir /s /q leptonica

echo Downloading Leptonica %LEPTONICA_VERSION%...
curl -L -o leptonica.zip https://github.com/DanBloomberg/leptonica/archive/refs/tags/%LEPTONICA_VERSION%.zip

echo Extracting...
tar -xf leptonica.zip
move leptonica-%LEPTONICA_VERSION% leptonica
del leptonica.zip

cd leptonica
mkdir build
cd build

echo Configuring Leptonica...
cmake .. -G "Visual Studio 17 2022" -A x64 ^
    -DCMAKE_BUILD_TYPE=Release ^
    -DCMAKE_INSTALL_PREFIX=%INSTALL_DIR%\leptonica ^
    -DBUILD_SHARED_LIBS=ON ^
    -DSW_BUILD=OFF

if %ERRORLEVEL% NEQ 0 (
    echo ERROR: Leptonica CMake failed!
    pause
    exit /b 1
)

echo Building Leptonica (single thread)...
echo Started at: %time%
cmake --build . --config Release --target INSTALL -- /m:1

if %ERRORLEVEL% NEQ 0 (
    echo ERROR: Leptonica build failed!
    pause
    exit /b 1
)

echo Completed at: %time%
echo [OK] Leptonica built!

REM Create CMake config for Tesseract to find it
if not exist "%INSTALL_DIR%\leptonica\lib\cmake\leptonica" (
    mkdir "%INSTALL_DIR%\leptonica\lib\cmake\leptonica"
)

(
echo # Leptonica CMake Config
echo set(Leptonica_INCLUDE_DIRS "%INSTALL_DIR%\leptonica\include"^)
echo set(Leptonica_LIBRARIES "%INSTALL_DIR%\leptonica\lib\leptonica-1.85.0.lib"^)
echo set(Leptonica_FOUND TRUE^)
) > "%INSTALL_DIR%\leptonica\lib\cmake\leptonica\LeptonicaConfig.cmake"

echo.
echo ============================================================
echo PART 2 COMPLETE - Leptonica built
echo ============================================================
pause

REM ====================================================================
REM PART 3: BUILD TESSERACT
REM ====================================================================
echo.
echo ============================================================
echo PART 3 of 6: BUILDING TESSERACT %TESSERACT_VERSION%
echo ============================================================
echo.
pause

cd /d %BUILD_DIR%

if exist tesseract rmdir /s /q tesseract

echo Downloading Tesseract %TESSERACT_VERSION%...
curl -L -o tesseract.zip https://github.com/tesseract-ocr/tesseract/archive/refs/tags/%TESSERACT_VERSION%.zip

echo Extracting...
tar -xf tesseract.zip
move tesseract-%TESSERACT_VERSION% tesseract
del tesseract.zip

cd tesseract
mkdir build
cd build

echo Configuring Tesseract...
cmake .. -G "Visual Studio 17 2022" -A x64 ^
    -DCMAKE_BUILD_TYPE=Release ^
    -DCMAKE_INSTALL_PREFIX=%INSTALL_DIR%\tesseract ^
    -DBUILD_SHARED_LIBS=ON ^
    -DLeptonica_DIR=%INSTALL_DIR%\leptonica\lib\cmake\leptonica ^
    -DCMAKE_PREFIX_PATH=%INSTALL_DIR%\leptonica ^
    -DSW_BUILD=OFF ^
    -DBUILD_TRAINING_TOOLS=OFF ^
    -DBUILD_TESTS=OFF ^
    -DDISABLED_LEGACY_ENGINE=OFF ^
    -DOPENMP_BUILD=ON

if %ERRORLEVEL% NEQ 0 (
    echo Trying with explicit Leptonica paths...
    cmake .. -G "Visual Studio 17 2022" -A x64 ^
        -DCMAKE_BUILD_TYPE=Release ^
        -DCMAKE_INSTALL_PREFIX=%INSTALL_DIR%\tesseract ^
        -DBUILD_SHARED_LIBS=ON ^
        -DLeptonica_INCLUDE_DIR=%INSTALL_DIR%\leptonica\include ^
        -DLeptonica_LIBRARY=%INSTALL_DIR%\leptonica\lib\leptonica-1.85.0.lib ^
        -DSW_BUILD=OFF ^
        -DBUILD_TRAINING_TOOLS=OFF ^
        -DBUILD_TESTS=OFF
    
    if %ERRORLEVEL% NEQ 0 (
        echo ERROR: Tesseract configuration failed!
        pause
        exit /b 1
    )
)

echo Building Tesseract (single thread)...
echo Started at: %time%
cmake --build . --config Release --target INSTALL -- /m:1

if %ERRORLEVEL% NEQ 0 (
    echo ERROR: Tesseract build failed!
    pause
    exit /b 1
)

echo Completed at: %time%
echo [OK] Tesseract built!

echo.
echo ============================================================
echo PART 3 COMPLETE - Tesseract built
echo ============================================================
pause

REM ====================================================================
REM PART 4: DOWNLOAD TESSDATA TO CORRECT LOCATION
REM ====================================================================
echo.
echo ============================================================
echo PART 4 of 6: SETTING UP TESSDATA
echo ============================================================
echo.
echo CRITICAL: tessdata goes in bin\tessdata for the code to find it!
echo.
pause

cd /d %PROJECT_DIR%\bin

if not exist tessdata mkdir tessdata

echo Downloading English language data (best quality)...
curl -L -o tessdata\eng.traineddata https://github.com/tesseract-ocr/tessdata_best/raw/main/eng.traineddata

echo Downloading orientation detection data...
curl -L -o tessdata\osd.traineddata https://github.com/tesseract-ocr/tessdata_best/raw/main/osd.traineddata

echo.
echo Verifying tessdata location...
if exist tessdata\eng.traineddata (
    echo [OK] tessdata\eng.traineddata in place
    echo Full path: %PROJECT_DIR%\bin\tessdata\
) else (
    echo [ERROR] tessdata not properly installed!
)

echo.
echo ============================================================
echo PART 4 COMPLETE - tessdata configured
echo ============================================================
pause

REM ====================================================================
REM PART 5: COPY ALL DLLS INCLUDING IMAGE FORMATS
REM ====================================================================
echo.
echo ============================================================
echo PART 5 of 6: INSTALLING ALL DLLS
echo ============================================================
echo.
pause

cd /d %PROJECT_DIR%

echo Copying OpenCV DLLs...
xcopy /Y "%INSTALL_DIR%\opencv\x64\vc17\bin\*.dll" "bin\" >nul 2>&1

echo Copying Leptonica DLLs...
xcopy /Y "%INSTALL_DIR%\leptonica\bin\*.dll" "bin\" >nul 2>&1

echo Copying Tesseract DLLs...
xcopy /Y "%INSTALL_DIR%\tesseract\bin\*.dll" "bin\" >nul 2>&1

echo Copying libraries...
xcopy /Y "%INSTALL_DIR%\opencv\x64\vc17\lib\*.lib" "deps\lib\" >nul 2>&1
xcopy /Y "%INSTALL_DIR%\leptonica\lib\*.lib" "deps\lib\" >nul 2>&1
xcopy /Y "%INSTALL_DIR%\tesseract\lib\*.lib" "deps\lib\" >nul 2>&1

echo Copying headers...
xcopy /E /Y "%INSTALL_DIR%\opencv\include\*" "deps\include\" >nul 2>&1
xcopy /E /Y "%INSTALL_DIR%\leptonica\include\*" "deps\include\" >nul 2>&1
xcopy /E /Y "%INSTALL_DIR%\tesseract\include\*" "deps\include\" >nul 2>&1

echo.
echo Copying IMAGE FORMAT DLLs from vcpkg (REQUIRED!)...
cd bin

REM Essential image format DLLs
set ESSENTIAL_DLLS=libpng16.dll jpeg62.dll tiff.dll libwebp.dll zlib1.dll

for %%D in (%ESSENTIAL_DLLS%) do (
    if not exist "%%D" (
        if exist "%VCPKG_BIN%\%%D" (
            copy /Y "%VCPKG_BIN%\%%D" . >nul
            echo [COPIED] %%D - ESSENTIAL
        ) else (
            echo [WARNING] %%D not found - OCR may fail!
        )
    ) else (
        echo [EXISTS] %%D
    )
)

REM Additional DLLs
set EXTRA_DLLS=turbojpeg.dll gif.dll openjp2.dll libwebpdecoder.dll libwebpdemux.dll libwebpmux.dll liblzma.dll bz2.dll libsharpyuv.dll archive.dll lz4.dll zstd.dll

for %%D in (%EXTRA_DLLS%) do (
    if exist "%VCPKG_BIN%\%%D" (
        copy /Y "%VCPKG_BIN%\%%D" . >nul 2>&1
        echo [COPIED] %%D
    )
)

REM Try alternate names
xcopy /Y "%VCPKG_BIN%\png*.dll" . >nul 2>&1
xcopy /Y "%VCPKG_BIN%\jpeg*.dll" . >nul 2>&1
xcopy /Y "%VCPKG_BIN%\tiff*.dll" . >nul 2>&1
xcopy /Y "%VCPKG_BIN%\zlib*.dll" . >nul 2>&1

echo.
echo Verifying critical DLLs...
if exist opencv_world*.dll echo [OK] OpenCV world DLL
if exist tesseract*.dll echo [OK] Tesseract DLL
if exist leptonica*.dll echo [OK] Leptonica DLL
if exist libpng*.dll echo [OK] PNG support
if exist jpeg*.dll echo [OK] JPEG support
if exist tiff*.dll echo [OK] TIFF support

echo.
echo ============================================================
echo PART 5 COMPLETE - All DLLs installed
echo ============================================================
pause

REM ====================================================================
REM PART 6: BUILD ACCELERATOR_SHAREDMEM.PYD
REM ====================================================================
echo.
echo ============================================================
echo PART 6 of 6: BUILDING ACCELERATOR_SHAREDMEM.PYD
echo ============================================================
echo.
echo This creates the final Python module
echo NOTE: Code expects tessdata in bin\tessdata
echo.
pause

cd /d %PROJECT_DIR%

if not exist accelerator_sharedmem.cpp (
    echo ERROR: accelerator_sharedmem.cpp not found!
    echo Copy it from GitHub repo or backup!
    pause
    exit /b 1
)

echo Installing Python dependencies...
%PYTHON_HOME%\python.exe -m pip install pybind11 numpy >nul 2>&1

echo Getting Python paths...
for /f "delims=" %%i in ('%PYTHON_HOME%\python.exe -c "import sysconfig; print(sysconfig.get_path('include'))"') do set PYTHON_INCLUDE=%%i
for /f "delims=" %%i in ('%PYTHON_HOME%\python.exe -c "import pybind11; print(pybind11.get_include())"') do set PYBIND11_INCLUDE=%%i
for /f "delims=" %%i in ('%PYTHON_HOME%\python.exe -c "import numpy; print(numpy.get_include())"') do set NUMPY_INCLUDE=%%i

echo.
echo Compiling with AVX2 optimizations (NOT AVX512 flag)...
cl /MD /O2 /Ob2 /Oi /Ot /Oy /GT /GL /GS- /fp:fast /arch:AVX2 ^
    /I"%PYTHON_INCLUDE%" ^
    /I"%PYBIND11_INCLUDE%" ^
    /I"%NUMPY_INCLUDE%" ^
    /I"deps\include" ^
    /I"deps\opencv\include" ^
    /I"deps\tesseract\include" ^
    /I"deps\leptonica\include" ^
    /D_CRT_SECURE_NO_WARNINGS ^
    /DNDEBUG ^
    /c accelerator_sharedmem.cpp

if %ERRORLEVEL% NEQ 0 (
    echo ERROR: Compilation failed!
    pause
    exit /b 1
)

echo Linking...
link /DLL /OUT:bin\accelerator_sharedmem.pyd ^
    /LIBPATH:"%PYTHON_HOME%\libs" ^
    /LIBPATH:"deps\lib" ^
    /LIBPATH:"deps\opencv\x64\vc17\lib" ^
    /LIBPATH:"deps\tesseract\lib" ^
    /LIBPATH:"deps\leptonica\lib" ^
    accelerator_sharedmem.obj ^
    python311.lib ^
    opencv_world*.lib ^
    tesseract*.lib ^
    leptonica*.lib ^
    /LTCG

if %ERRORLEVEL% NEQ 0 (
    echo ERROR: Linking failed!
    pause
    exit /b 1
)

del accelerator_sharedmem.obj
echo [OK] accelerator_sharedmem.pyd built!

echo.
echo ============================================================
echo PART 6 COMPLETE - PYD module built
echo ============================================================
pause

REM ====================================================================
REM FINAL TEST
REM ====================================================================
echo.
echo ============================================================
echo FINAL TEST
echo ============================================================
echo.

cd bin

echo Testing module load...
%PYTHON_HOME%\python.exe -c "import accelerator_sharedmem; print('[SUCCESS] Module loads!')" 2>&1

if %ERRORLEVEL% NEQ 0 (
    echo.
    echo [FAILED] Module won't load. Checking why...
    echo.
    echo Testing detailed import...
    %PYTHON_HOME%\python.exe -c "import accelerator_sharedmem"
    echo.
    echo Critical files check:
    if exist accelerator_sharedmem.pyd echo [OK] .pyd exists
    if exist opencv_world*.dll echo [OK] OpenCV DLL exists
    if exist tesseract*.dll echo [OK] Tesseract DLL exists
    if exist tessdata\eng.traineddata echo [OK] tessdata exists
    echo.
) else (
    echo.
    echo Testing OCR initialization...
    %PYTHON_HOME%\python.exe -c "import accelerator_sharedmem; a = accelerator_sharedmem.AcceleratorSharedMemPipeline(); print('[SUCCESS] OCR pipeline initialized!')" 2>&1
    
    echo.
    echo ============================================================
    echo COMPLETE SUCCESS!
    echo ============================================================
    echo.
    echo Everything built and working:
    echo - OpenCV %OPENCV_VERSION% (optimized)
    echo - Leptonica %LEPTONICA_VERSION%
    echo - Tesseract %TESSERACT_VERSION%
    echo - accelerator_sharedmem.pyd
    echo - tessdata in bin\tessdata\
    echo - All image format DLLs
    echo.
    echo The accelerator looks for tessdata in:
    echo 1. bin\tessdata (where we put it)
    echo 2. C:\TESTRADE\tessdata (legacy)
    echo 3. Current exe directory\tessdata
    echo.
    echo Ready for 50ms OCR on the 9950X3D BEAST!
)

echo.
echo Build artifacts saved in: %ARTIFACTS_DIR%
echo.
pause