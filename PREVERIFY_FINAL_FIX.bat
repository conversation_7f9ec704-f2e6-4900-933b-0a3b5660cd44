@echo off
REM ====================================================================
REM PRE-BUILD VERIFICATION SCRIPT - FINAL FIX
REM ====================================================================
REM Fixed to check actual VS Developer prompt environment variables
REM ====================================================================

echo ============================================================
echo PRE-BUILD VERIFICATION FOR 9950X3D BUILD - FINAL FIX
echo ============================================================
echo.
echo This script verifies ALL prerequisites BEFORE building
echo.

REM Check if in VS Developer Command Prompt - FIXED CHECK
echo Checking for Visual Studio Developer Environment...
set IN_VS_PROMPT=0

REM Check multiple variables that VS Developer prompt sets
if defined VSCMD_ARG_HOST_ARCH set IN_VS_PROMPT=1
if defined VCToolsVersion set IN_VS_PROMPT=1
if defined VCINSTALLDIR set IN_VS_PROMPT=1
if defined VisualStudioVersion set IN_VS_PROMPT=1

REM Also check if cl.exe is available
where cl.exe >nul 2>&1
if %ERRORLEVEL% EQU 0 set IN_VS_PROMPT=1

if %IN_VS_PROMPT% EQU 0 (
    echo ============================================================
    echo CRITICAL: NOT IN VISUAL STUDIO DEVELOPER PROMPT!
    echo ============================================================
    echo.
    echo You MUST run this from:
    echo   "x64 Native Tools Command Prompt for VS 2022"
    echo.
    echo Steps:
    echo 1. Press Windows key
    echo 2. Type: x64 native
    echo 3. Click: "x64 Native Tools Command Prompt for VS 2022"
    echo 4. Navigate to C:\TANK
    echo 5. Run this script again
    echo.
    echo ============================================================
    pause
    exit /b 1
)

echo [OK] Visual Studio Developer environment detected
echo.
pause

set ERRORS=0
set WARNINGS=0

REM ====================================================================
REM SECTION 1: SYSTEM REQUIREMENTS
REM ====================================================================
echo.
echo ============================================================
echo SECTION 1: SYSTEM REQUIREMENTS
echo ============================================================
echo.

REM Check Visual Studio 2022 compiler
echo Checking Visual Studio 2022 compiler...
where cl.exe >nul 2>&1
if %ERRORLEVEL% NEQ 0 (
    echo [ERROR] cl.exe not found in PATH!
    set /a ERRORS+=1
) else (
    REM Get compiler version
    for /f "tokens=*" %%i in ('cl.exe 2^>^&1 ^| findstr /C:"Version"') do set CL_LINE=%%i
    echo [INFO] Compiler: %CL_LINE%
    
    REM Check if it's VS2022 (version 19.3x or higher)
    echo %CL_LINE% | findstr "19.3" >nul
    if %ERRORLEVEL% EQU 0 (
        echo [OK] Visual Studio 2022 compiler confirmed
    ) else (
        echo %CL_LINE% | findstr "19.4" >nul
        if %ERRORLEVEL% EQU 0 (
            echo [OK] Visual Studio 2022 compiler confirmed
        ) else (
            echo [WARNING] May not be VS2022 compiler
            set /a WARNINGS+=1
        )
    )
)

REM Check CMake
echo Checking CMake...
where cmake.exe >nul 2>&1
if %ERRORLEVEL% NEQ 0 (
    echo [ERROR] CMake not found in PATH!
    echo        Install from: https://cmake.org/download/
    set /a ERRORS+=1
) else (
    cmake --version | findstr "version" >nul
    if %ERRORLEVEL% EQU 0 (
        echo [OK] CMake found
    )
)

REM Check Python 3.11 - FIXED VERSION
echo Checking Python 3.11...
set PYTHON_FOUND=0
set PYTHON_PATH=

REM First check C:\Python311
if exist "C:\Python311\python.exe" (
    set PYTHON_PATH=C:\Python311\python.exe
) else if exist "C:\Python\Python311\python.exe" (
    set PYTHON_PATH=C:\Python\Python311\python.exe
) else (
    REM Check if python is in PATH
    where python.exe >nul 2>&1
    if %ERRORLEVEL% EQU 0 (
        for /f "tokens=*" %%i in ('where python.exe') do (
            if not defined PYTHON_PATH set PYTHON_PATH=%%i
        )
    )
)

if defined PYTHON_PATH (
    REM Get Python version
    for /f "tokens=2" %%i in ('"%PYTHON_PATH%" --version 2^>^&1') do set PYTHON_VERSION=%%i
    echo [INFO] Found Python at: %PYTHON_PATH%
    echo [INFO] Python version: %PYTHON_VERSION%
    
    REM Check if version starts with 3.11
    echo %PYTHON_VERSION% | findstr /B "3.11" >nul
    if %ERRORLEVEL% EQU 0 (
        echo [OK] Python 3.11.x found
        set PYTHON_FOUND=1
    ) else (
        echo [WARNING] Python found but not version 3.11.x
        set /a WARNINGS+=1
    )
) else (
    echo [ERROR] Python 3.11 not found!
    echo        Expected at C:\Python311\ or in PATH
    echo        Download from: https://www.python.org/downloads/release/python-3119/
    set /a ERRORS+=1
)

REM Check vcpkg
echo Checking vcpkg...
if exist "C:\tools\vcpkg\vcpkg.exe" (
    echo [OK] vcpkg found at C:\tools\vcpkg
) else if exist "C:\vcpkg\vcpkg.exe" (
    echo [OK] vcpkg found at C:\vcpkg
) else (
    where vcpkg.exe >nul 2>&1
    if %ERRORLEVEL% EQU 0 (
        echo [OK] vcpkg found in PATH
    ) else (
        echo [WARNING] vcpkg not found
        echo          Install: git clone https://github.com/Microsoft/vcpkg.git C:\tools\vcpkg
        echo          Then run: C:\tools\vcpkg\bootstrap-vcpkg.bat
        set /a WARNINGS+=1
    )
)

REM Check curl
echo Checking curl...
where curl.exe >nul 2>&1
if %ERRORLEVEL% NEQ 0 (
    echo [ERROR] curl.exe not found!
    set /a ERRORS+=1
) else (
    echo [OK] curl found
)

REM Check tar
echo Checking tar...
where tar.exe >nul 2>&1
if %ERRORLEVEL% NEQ 0 (
    echo [ERROR] tar.exe not found!
    set /a ERRORS+=1
) else (
    echo [OK] tar found
)

REM ====================================================================
REM SECTION 2: DIRECTORY STRUCTURE
REM ====================================================================
echo.
echo ============================================================
echo SECTION 2: DIRECTORY STRUCTURE
echo ============================================================
echo.

set PROJECT_DIR=C:\TANK\ocr_accelerator
set BUILD_DIR=%PROJECT_DIR%\source
set INSTALL_DIR=%PROJECT_DIR%\deps

echo Creating project directories...
if not exist "%PROJECT_DIR%" mkdir "%PROJECT_DIR%"
if not exist "%BUILD_DIR%" mkdir "%BUILD_DIR%"
if not exist "%INSTALL_DIR%" mkdir "%INSTALL_DIR%"
if not exist "%PROJECT_DIR%\bin" mkdir "%PROJECT_DIR%\bin"
if not exist "%PROJECT_DIR%\deps\lib" mkdir "%PROJECT_DIR%\deps\lib"
if not exist "%PROJECT_DIR%\deps\include" mkdir "%PROJECT_DIR%\deps\include"
if not exist "%PROJECT_DIR%\bin\tessdata" mkdir "%PROJECT_DIR%\bin\tessdata"

echo [OK] Directory structure ready

REM ====================================================================
REM SECTION 3: SOURCE CODE VERIFICATION
REM ====================================================================
echo.
echo ============================================================
echo SECTION 3: SOURCE CODE VERIFICATION
echo ============================================================
echo.

echo Checking for accelerator_sharedmem.cpp...
if exist "%PROJECT_DIR%\accelerator_sharedmem.cpp" (
    echo [OK] accelerator_sharedmem.cpp found
    for %%F in ("%PROJECT_DIR%\accelerator_sharedmem.cpp") do echo      Size: %%~zF bytes
) else (
    echo [WARNING] accelerator_sharedmem.cpp NOT FOUND!
    echo          Copy from backup or GitHub repo
    set /a WARNINGS+=1
)

REM ====================================================================
REM SECTION 4: PYTHON DEPENDENCIES
REM ====================================================================
echo.
echo ============================================================
echo SECTION 4: PYTHON DEPENDENCIES
echo ============================================================
echo.

if %PYTHON_FOUND% EQU 1 (
    echo Checking pybind11...
    "%PYTHON_PATH%" -c "import pybind11; print('[OK] pybind11 installed')" 2>nul
    if %ERRORLEVEL% NEQ 0 (
        echo [INFO] Installing pybind11...
        "%PYTHON_PATH%" -m pip install pybind11 >nul 2>&1
        if %ERRORLEVEL% EQU 0 (
            echo [OK] pybind11 installed
        ) else (
            echo [WARNING] Could not install pybind11
            set /a WARNINGS+=1
        )
    )
    
    echo Checking numpy...
    "%PYTHON_PATH%" -c "import numpy; print('[OK] numpy installed')" 2>nul
    if %ERRORLEVEL% NEQ 0 (
        echo [INFO] Installing numpy...
        "%PYTHON_PATH%" -m pip install numpy >nul 2>&1
        if %ERRORLEVEL% EQU 0 (
            echo [OK] numpy installed
        ) else (
            echo [WARNING] Could not install numpy
            set /a WARNINGS+=1
        )
    )
)

REM ====================================================================
REM SECTION 5: COMPILER FLAGS TEST
REM ====================================================================
echo.
echo ============================================================
echo SECTION 5: COMPILER FLAGS VERIFICATION
echo ============================================================
echo.

echo Testing compiler optimization flags...
echo int main() { return 0; } > test.cpp

echo Testing /arch:AVX512...
cl.exe /c /arch:AVX512 test.cpp >nul 2>&1
if %ERRORLEVEL% EQU 0 (
    echo [OK] /arch:AVX512 supported
) else (
    echo [INFO] /arch:AVX512 not supported, will use AVX2
)

echo Testing /favor:AMD64...
cl.exe /c /favor:AMD64 test.cpp >nul 2>&1
if %ERRORLEVEL% EQU 0 (
    echo [OK] /favor:AMD64 supported
) else (
    echo [INFO] /favor:AMD64 not supported
)

echo Testing /Ob3...
cl.exe /c /Ob3 test.cpp >nul 2>&1
if %ERRORLEVEL% EQU 0 (
    echo [OK] /Ob3 aggressive inlining supported
) else (
    echo [INFO] /Ob3 not supported, will use /Ob2
)

del test.cpp >nul 2>&1
del test.obj >nul 2>&1

REM ====================================================================
REM SECTION 6: IMAGE LIBRARIES (SIMPLIFIED)
REM ====================================================================
echo.
echo ============================================================
echo SECTION 6: IMAGE FORMAT LIBRARIES
echo ============================================================
echo.

echo Checking for image format DLLs...
set VCPKG_FOUND=0
set VCPKG_BIN=

if exist "C:\tools\vcpkg\installed\x64-windows\bin" (
    set VCPKG_BIN=C:\tools\vcpkg\installed\x64-windows\bin
    set VCPKG_FOUND=1
) else if exist "C:\vcpkg\installed\x64-windows\bin" (
    set VCPKG_BIN=C:\vcpkg\installed\x64-windows\bin
    set VCPKG_FOUND=1
)

if %VCPKG_FOUND% EQU 1 (
    echo Checking vcpkg libraries at: %VCPKG_BIN%
    
    if exist "%VCPKG_BIN%\libpng16.dll" (
        echo [OK] libpng16.dll found
    ) else (
        echo [INFO] libpng16.dll not found (PNG support)
    )
    
    if exist "%VCPKG_BIN%\jpeg62.dll" (
        echo [OK] jpeg62.dll found
    ) else if exist "%VCPKG_BIN%\turbojpeg.dll" (
        echo [OK] turbojpeg.dll found (JPEG support)
    ) else (
        echo [INFO] JPEG libraries not found
    )
    
    if exist "%VCPKG_BIN%\tiff.dll" (
        echo [OK] tiff.dll found
    ) else (
        echo [INFO] tiff.dll not found (TIFF support)
    )
) else (
    echo [INFO] vcpkg packages directory not found
    echo        Image libraries will be built with OpenCV
)

REM ====================================================================
REM SECTION 7: DISK SPACE CHECK
REM ====================================================================
echo.
echo ============================================================
echo SECTION 7: DISK SPACE VERIFICATION
echo ============================================================
echo.

wmic logicaldisk where caption="C:" get freespace /value 2>nul | findstr FreeSpace >temp.txt
for /f "tokens=2 delims==" %%a in (temp.txt) do set FREE_BYTES=%%a
del temp.txt >nul 2>&1

if defined FREE_BYTES (
    REM Simple check - if more than 10 digits, we have at least 10GB
    set DIGIT_COUNT=0
    set TEMP=%FREE_BYTES%
    :count
    if defined TEMP (
        set TEMP=%TEMP:~1%
        set /a DIGIT_COUNT+=1
        if %DIGIT_COUNT% LSS 20 goto count
    )
    
    if %DIGIT_COUNT% GTR 10 (
        echo [OK] Sufficient disk space available
    ) else (
        echo [WARNING] May have low disk space
        set /a WARNINGS+=1
    )
) else (
    echo [INFO] Could not check disk space
)

REM ====================================================================
REM FINAL SUMMARY
REM ====================================================================
echo.
echo ============================================================
echo VERIFICATION SUMMARY
echo ============================================================
echo.
echo ERRORS:   %ERRORS%
echo WARNINGS: %WARNINGS%
echo.

if %ERRORS% GTR 0 (
    echo ============================================================
    echo CRITICAL: %ERRORS% ERRORS FOUND!
    echo ============================================================
    echo.
    echo Fix these errors before proceeding:
    echo - Install missing tools
    echo - Ensure running from VS2022 Developer Prompt
    echo.
    echo DO NOT PROCEED WITH BUILD!
    echo ============================================================
) else if %WARNINGS% GTR 0 (
    echo ============================================================
    echo READY WITH %WARNINGS% WARNINGS
    echo ============================================================
    echo.
    echo Build can proceed. Consider fixing warnings for best results.
    echo.
    echo Next step: Run BUILD_ULTIMATE_VERIFIED.bat
    echo ============================================================
) else (
    echo ============================================================
    echo PERFECT! ALL CHECKS PASSED!
    echo ============================================================
    echo.
    echo System is FULLY ready for the 9950X3D build!
    echo.
    echo Next step: Run BUILD_ULTIMATE_VERIFIED.bat
    echo ============================================================
)

echo.
pause