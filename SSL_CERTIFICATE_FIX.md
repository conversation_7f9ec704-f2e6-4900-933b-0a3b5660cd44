# SSL Certificate Fix for TANK/TESTRADE on Windows

## Current Issue
PriceFetchingService fails to connect to Alpaca's WebSocket due to SSL certificate verification failure on Windows.

## Temporary Fix (CURRENTLY APPLIED)
The batch file `start_tank_sealed.bat` currently has:
```batch
set PYTHONHTTPSVERIFY=0
```
⚠️ **WARNING: This disables SSL verification and should NOT be used in production!**

## Permanent Solutions

### Option 1: Use Certifi Bundle (RECOMMENDED)
1. Verify certifi is installed:
   ```
   pip install --upgrade certifi
   ```

2. In `start_tank_sealed.bat`, replace the temporary fix with:
   ```batch
   REM Use certifi's certificate bundle
   set SSL_CERT_FILE=C:\TANK\.venv\Lib\site-packages\certifi\cacert.pem
   ```

### Option 2: Use Windows Certificate Store
1. Install pip-system-certs:
   ```
   pip install pip-system-certs
   ```
   This makes Python use Windows' certificate store automatically.

2. Remove the `PYTHONHTTPSVERIFY=0` line from the batch file.

### Option 3: Update Windows Certificates
1. Run Windows Update to ensure certificates are current
2. Download and install latest certificates from Microsoft
3. Remove the temporary fix from batch file

## Testing
After applying any permanent fix, test with:
```batch
cd C:\TANK
.venv\Scripts\python.exe test_alpaca_connection.py
```

The connection should succeed WITHOUT the SSL warning.

## Implementation Notes
- The PriceFetchingService code is correct and follows standard practices
- No code changes needed - this is purely an environment configuration issue
- The issue affects any Python application connecting to secure WebSockets on Windows

## References
- Alpaca WebSocket Documentation: https://docs.alpaca.markets/docs/websocket-streaming
- Python SSL Module: https://docs.python.org/3/library/ssl.html
- Certifi Package: https://pypi.org/project/certifi/