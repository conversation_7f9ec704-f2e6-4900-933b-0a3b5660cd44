@echo off
REM Check what environment variables are actually set in VS Developer Prompt
echo ============================================================
echo CHECKING VISUAL STUDIO ENVIRONMENT VARIABLES
echo ============================================================
echo.

echo Checking common VS environment variables...
echo.

if defined VSINSTALLDIR (
    echo VSINSTALLDIR = %VSINSTALLDIR%
) else (
    echo VSINSTALLDIR = NOT SET
)

if defined VSCMD_ARG_HOST_ARCH (
    echo VSCMD_ARG_HOST_ARCH = %VSCMD_ARG_HOST_ARCH%
) else (
    echo VSCMD_ARG_HOST_ARCH = NOT SET
)

if defined VSCMD_ARG_TGT_ARCH (
    echo VSCMD_ARG_TGT_ARCH = %VSCMD_ARG_TGT_ARCH%
) else (
    echo VSCMD_ARG_TGT_ARCH = NOT SET
)

if defined DevEnvDir (
    echo DevEnvDir = %DevEnvDir%
) else (
    echo DevEnvDir = NOT SET
)

if defined VCToolsVersion (
    echo VCToolsVersion = %VCToolsVersion%
) else (
    echo VCToolsVersion = NOT SET
)

if defined VisualStudioVersion (
    echo VisualStudioVersion = %VisualStudioVersion%
) else (
    echo VisualStudioVersion = NOT SET
)

if defined VCINSTALLDIR (
    echo VCINSTALLDIR = %VCINSTALLDIR%
) else (
    echo VCINSTALLDIR = NOT SET
)

if defined Platform (
    echo Platform = %Platform%
) else (
    echo Platform = NOT SET
)

echo.
echo Checking if cl.exe is available...
where cl.exe >nul 2>&1
if %ERRORLEVEL% EQU 0 (
    echo cl.exe FOUND in PATH
    for /f "tokens=*" %%i in ('where cl.exe') do echo Location: %%i
) else (
    echo cl.exe NOT FOUND
)

echo.
echo Checking if we're in ANY Developer prompt...
if defined VSCMD_ARG_HOST_ARCH (
    echo [OK] In VS Developer Command Prompt (VSCMD variables set)
) else if defined VCToolsVersion (
    echo [OK] In VS Developer Command Prompt (VCToolsVersion set)
) else if defined VCINSTALLDIR (
    echo [OK] In VS Developer Command Prompt (VCINSTALLDIR set)
) else (
    where cl.exe >nul 2>&1
    if %ERRORLEVEL% EQU 0 (
        echo [MAYBE] cl.exe found but VS variables not set
        echo         You might be in a Developer prompt
    ) else (
        echo [NO] Not in VS Developer Command Prompt
        echo      No VS environment variables detected
    )
)

echo.
pause