@echo off
REM ====================================================================
REM ULTIMATE 9950X3D BUILD WITH VERIFICATION AT EVERY STEP
REM ====================================================================
REM Mission Critical: Each section verified before proceeding
REM ====================================================================

echo ============================================================
echo ULTIMATE 9950X3D BUILD - MISSION CRITICAL VERSION
echo ============================================================
echo.
echo This build verifies EVERY step before proceeding
echo Failure points are caught immediately
echo.
pause

set OPENCV_VERSION=4.10.0
set TESSERACT_VERSION=5.5.1
set LEPTONICA_VERSION=1.85.0
set BUILD_DIR=C:\TANK\ocr_accelerator\source
set INSTALL_DIR=C:\TANK\ocr_accelerator\deps
set PROJECT_DIR=C:\TANK\ocr_accelerator
set VCPKG_BIN=C:\tools\vcpkg\installed\x64-windows\bin
set PYTHON_HOME=C:\Python311

REM Ultimate compiler flags
set ULTIMATE_FLAGS=/O2 /Ob3 /Oi /Ot /Oy /GT /GL /GS- /fp:fast /arch:AVX512 /favor:AMD64 /Qpar

REM Create all directories first
echo Creating directory structure...
if not exist %BUILD_DIR% mkdir %BUILD_DIR%
if not exist %INSTALL_DIR% mkdir %INSTALL_DIR%
if not exist %PROJECT_DIR%\bin mkdir %PROJECT_DIR%\bin
if not exist %PROJECT_DIR%\deps\lib mkdir %PROJECT_DIR%\deps\lib
if not exist %PROJECT_DIR%\deps\include mkdir %PROJECT_DIR%\deps\include

REM ====================================================================
REM PART 1: OPENCV BUILD WITH VERIFICATION
REM ====================================================================
echo.
echo ============================================================
echo PART 1: OPENCV BUILD
echo ============================================================
echo.
echo Pre-verification for OpenCV...

REM Check if already built
if exist "%PROJECT_DIR%\bin\opencv_world4100.dll" (
    echo [INFO] opencv_world4100.dll already exists
    for %%F in ("%PROJECT_DIR%\bin\opencv_world4100.dll") do (
        set SIZE=%%~zF
        set /a SIZE_MB=!SIZE!/1048576
        echo       Size: !SIZE_MB! MB
    )
    choice /C YN /T 10 /D N /M "Skip OpenCV build"
    if errorlevel 2 goto PART2_LEPTONICA
)

REM Verify download URL before attempting
echo Verifying OpenCV download URL...
curl -I -s -o nul -w "%%{http_code}" https://github.com/opencv/opencv/archive/refs/tags/%OPENCV_VERSION%.zip > temp.txt
set /p HTTP_CODE=<temp.txt
del temp.txt
if not "%HTTP_CODE%"=="302" if not "%HTTP_CODE%"=="200" (
    echo [ERROR] OpenCV URL returns HTTP %HTTP_CODE%
    echo        URL may be wrong or GitHub is down
    pause
    exit /b 1
)
echo [OK] OpenCV URL verified (HTTP %HTTP_CODE%)

cd /d %BUILD_DIR%

if not exist opencv\CMakeLists.txt (
    echo Downloading OpenCV %OPENCV_VERSION%...
    curl -L -o opencv.zip https://github.com/opencv/opencv/archive/refs/tags/%OPENCV_VERSION%.zip
    if %ERRORLEVEL% NEQ 0 (
        echo [ERROR] OpenCV download failed!
        pause
        exit /b 1
    )
    
    echo Verifying download...
    if not exist opencv.zip (
        echo [ERROR] opencv.zip not created!
        pause
        exit /b 1
    )
    for %%F in (opencv.zip) do (
        if %%~zF LSS 1000000 (
            echo [ERROR] opencv.zip too small - download failed!
            pause
            exit /b 1
        )
        echo [OK] opencv.zip size: %%~zF bytes
    )
    
    echo Extracting OpenCV...
    tar -xf opencv.zip
    if %ERRORLEVEL% NEQ 0 (
        echo [ERROR] Extraction failed!
        pause
        exit /b 1
    )
    
    echo Verifying extraction...
    if exist opencv-%OPENCV_VERSION% (
        move opencv-%OPENCV_VERSION% opencv
        echo [OK] OpenCV extracted successfully
    ) else (
        echo [ERROR] Expected folder opencv-%OPENCV_VERSION% not found!
        dir /B
        pause
        exit /b 1
    )
    del opencv.zip

    echo Downloading OpenCV contrib...
    curl -L -o opencv_contrib.zip https://github.com/opencv/opencv_contrib/archive/refs/tags/%OPENCV_VERSION%.zip
    if %ERRORLEVEL% NEQ 0 (
        echo [ERROR] OpenCV contrib download failed!
        pause
        exit /b 1
    )
    
    tar -xf opencv_contrib.zip
    if exist opencv_contrib-%OPENCV_VERSION% (
        move opencv_contrib-%OPENCV_VERSION% opencv_contrib
        echo [OK] OpenCV contrib extracted
    ) else (
        echo [ERROR] opencv_contrib extraction failed!
        pause
        exit /b 1
    )
    del opencv_contrib.zip
)

REM Verify CMakeLists.txt exists
if not exist opencv\CMakeLists.txt (
    echo [ERROR] opencv\CMakeLists.txt not found!
    echo        OpenCV source is corrupted
    pause
    exit /b 1
)
echo [OK] OpenCV source verified

cd opencv
if not exist build mkdir build
cd build

echo.
echo Configuring OpenCV with CMake...
echo This will generate OpenCV.sln...

cmake .. -G "Visual Studio 17 2022" -A x64 ^
    -DCMAKE_BUILD_TYPE=Release ^
    -DCMAKE_INSTALL_PREFIX=%INSTALL_DIR%\opencv ^
    -DBUILD_opencv_world=ON ^
    -DBUILD_SHARED_LIBS=ON ^
    -DWITH_TBB=OFF ^
    -DWITH_OPENMP=ON ^
    -DWITH_IPP=OFF ^
    -DCPU_BASELINE=AVX2 ^
    -DCPU_DISPATCH=AVX2,FMA3,AVX_512F,AVX512_COMMON,AVX512_SKX ^
    -DENABLE_FAST_MATH=ON ^
    -DCV_ENABLE_INTRINSICS=ON ^
    -DBUILD_TESTS=OFF ^
    -DBUILD_PERF_TESTS=OFF ^
    -DBUILD_EXAMPLES=OFF ^
    -DBUILD_DOCS=OFF ^
    -DBUILD_opencv_apps=OFF ^
    -DBUILD_opencv_python2=OFF ^
    -DBUILD_opencv_python3=OFF ^
    -DOPENCV_EXTRA_MODULES_PATH=../../opencv_contrib/modules ^
    -DWITH_CUDA=OFF ^
    -DWITH_OPENCL=OFF ^
    -DWITH_PNG=ON ^
    -DWITH_JPEG=ON ^
    -DWITH_TIFF=ON ^
    -DENABLE_PRECOMPILED_HEADERS=OFF ^
    -DCMAKE_CXX_FLAGS_RELEASE="/O2 /Ob2 /Oi /Ot /Oy /GT /GL /GS- /fp:fast /arch:AVX512 /favor:AMD64" ^
    -DCMAKE_C_FLAGS_RELEASE="/O2 /Ob2 /Oi /Ot /Oy /GT /GL /GS- /fp:fast /arch:AVX512 /favor:AMD64"

if %ERRORLEVEL% NEQ 0 (
    echo [ERROR] OpenCV CMake configuration failed!
    echo        Check error messages above
    pause
    exit /b 1
)

REM Verify .sln was created
if not exist OpenCV.sln (
    echo [ERROR] OpenCV.sln was not generated!
    pause
    exit /b 1
)
echo [OK] OpenCV.sln generated successfully

echo.
echo Building OpenCV (single thread)...
echo Started at: %time%
cmake --build . --config Release --target INSTALL -- /m:1

if %ERRORLEVEL% NEQ 0 (
    echo [ERROR] OpenCV build failed!
    pause
    exit /b 1
)
echo Completed at: %time%

echo.
echo Verifying OpenCV installation...
cd /d %PROJECT_DIR%
xcopy /Y "%INSTALL_DIR%\opencv\x64\vc17\bin\*.dll" "bin\" >nul 2>&1
xcopy /Y "%INSTALL_DIR%\opencv\x64\vc17\lib\*.lib" "deps\lib\" >nul 2>&1
xcopy /E /Y "%INSTALL_DIR%\opencv\include\*" "deps\include\" >nul 2>&1

if exist "bin\opencv_world*.dll" (
    for %%F in (bin\opencv_world*.dll) do (
        echo [OK] OpenCV installed: %%~nxF (%%~zF bytes)
    )
) else (
    echo [ERROR] opencv_world DLL not found after build!
    pause
    exit /b 1
)

echo.
echo ============================================================
echo PART 1 COMPLETE - OpenCV verified and built
echo ============================================================
pause

:PART2_LEPTONICA
REM ====================================================================
REM PART 2: LEPTONICA BUILD WITH VERIFICATION
REM ====================================================================
echo.
echo ============================================================
echo PART 2: LEPTONICA BUILD
echo ============================================================
echo.
echo Pre-verification for Leptonica...

REM Verify URL
echo Verifying Leptonica download URL...
curl -I -s -o nul -w "%%{http_code}" https://github.com/DanBloomberg/leptonica/archive/refs/tags/%LEPTONICA_VERSION%.zip > temp.txt
set /p HTTP_CODE=<temp.txt
del temp.txt
if not "%HTTP_CODE%"=="302" if not "%HTTP_CODE%"=="200" (
    echo [ERROR] Leptonica URL returns HTTP %HTTP_CODE%
    pause
    exit /b 1
)
echo [OK] Leptonica URL verified

cd /d %BUILD_DIR%
if exist leptonica rmdir /s /q leptonica

echo Downloading Leptonica %LEPTONICA_VERSION%...
curl -L -o leptonica.zip https://github.com/DanBloomberg/leptonica/archive/refs/tags/%LEPTONICA_VERSION%.zip
if %ERRORLEVEL% NEQ 0 (
    echo [ERROR] Leptonica download failed!
    pause
    exit /b 1
)

echo Verifying download...
for %%F in (leptonica.zip) do (
    if %%~zF LSS 1000000 (
        echo [ERROR] leptonica.zip too small!
        pause
        exit /b 1
    )
    echo [OK] leptonica.zip size: %%~zF bytes
)

echo Extracting...
tar -xf leptonica.zip
if exist leptonica-%LEPTONICA_VERSION% (
    move leptonica-%LEPTONICA_VERSION% leptonica
    echo [OK] Leptonica extracted
) else (
    echo [ERROR] Leptonica extraction failed!
    pause
    exit /b 1
)
del leptonica.zip

cd leptonica
if not exist CMakeLists.txt (
    echo [ERROR] Leptonica CMakeLists.txt not found!
    pause
    exit /b 1
)
echo [OK] Leptonica source verified

mkdir build
cd build

echo Configuring Leptonica...
cmake .. -G "Visual Studio 17 2022" -A x64 ^
    -DCMAKE_BUILD_TYPE=Release ^
    -DCMAKE_INSTALL_PREFIX=%INSTALL_DIR%\leptonica ^
    -DBUILD_SHARED_LIBS=ON ^
    -DSW_BUILD=OFF ^
    -DCMAKE_CXX_FLAGS_RELEASE="/O2 /Ob2 /Oi /Ot /Oy /GT /GL /GS- /fp:fast /arch:AVX512 /favor:AMD64" ^
    -DCMAKE_C_FLAGS_RELEASE="/O2 /Ob2 /Oi /Ot /Oy /GT /GL /GS- /fp:fast /arch:AVX512 /favor:AMD64"

if %ERRORLEVEL% NEQ 0 (
    echo [ERROR] Leptonica CMake failed!
    pause
    exit /b 1
)
echo [OK] Leptonica configured

echo Building Leptonica...
cmake --build . --config Release --target INSTALL -- /m:1

if %ERRORLEVEL% NEQ 0 (
    echo [ERROR] Leptonica build failed!
    pause
    exit /b 1
)

echo Creating Leptonica CMake config for Tesseract...
if not exist "%INSTALL_DIR%\leptonica\lib\cmake\leptonica" (
    mkdir "%INSTALL_DIR%\leptonica\lib\cmake\leptonica"
)

(
echo # Leptonica CMake Config
echo set(Leptonica_INCLUDE_DIRS "%INSTALL_DIR%\leptonica\include"^)
echo set(Leptonica_LIBRARIES "%INSTALL_DIR%\leptonica\lib\leptonica-1.85.0.lib"^)
echo set(Leptonica_FOUND TRUE^)
) > "%INSTALL_DIR%\leptonica\lib\cmake\leptonica\LeptonicaConfig.cmake"

echo Verifying Leptonica installation...
if exist "%INSTALL_DIR%\leptonica\lib\leptonica-1.85.0.lib" (
    echo [OK] Leptonica library installed
) else (
    echo [ERROR] Leptonica library not found!
    dir "%INSTALL_DIR%\leptonica\lib\*.lib"
    pause
    exit /b 1
)

if exist "%INSTALL_DIR%\leptonica\bin\leptonica-1.85.0.dll" (
    echo [OK] Leptonica DLL found
) else (
    echo [WARNING] Leptonica DLL name may be different
    dir "%INSTALL_DIR%\leptonica\bin\*.dll"
)

echo.
echo ============================================================
echo PART 2 COMPLETE - Leptonica verified and built
echo ============================================================
pause

REM ====================================================================
REM PART 3: TESSERACT BUILD WITH VERIFICATION
REM ====================================================================
echo.
echo ============================================================
echo PART 3: TESSERACT BUILD
echo ============================================================
echo.
echo Pre-verification for Tesseract...

REM Verify Leptonica is ready
echo Verifying Leptonica is available for Tesseract...
if not exist "%INSTALL_DIR%\leptonica\lib\cmake\leptonica\LeptonicaConfig.cmake" (
    echo [ERROR] LeptonicaConfig.cmake not found!
    echo        Tesseract won't be able to find Leptonica
    pause
    exit /b 1
)
echo [OK] Leptonica config ready

cd /d %BUILD_DIR%
if exist tesseract rmdir /s /q tesseract

echo Downloading Tesseract %TESSERACT_VERSION%...
curl -L -o tesseract.zip https://github.com/tesseract-ocr/tesseract/archive/refs/tags/%TESSERACT_VERSION%.zip
if %ERRORLEVEL% NEQ 0 (
    echo [ERROR] Tesseract download failed!
    pause
    exit /b 1
)

echo Verifying download...
for %%F in (tesseract.zip) do (
    if %%~zF LSS 1000000 (
        echo [ERROR] tesseract.zip too small!
        pause
        exit /b 1
    )
    echo [OK] tesseract.zip size: %%~zF bytes
)

tar -xf tesseract.zip
if exist tesseract-%TESSERACT_VERSION% (
    move tesseract-%TESSERACT_VERSION% tesseract
    echo [OK] Tesseract extracted
) else (
    echo [ERROR] Tesseract extraction failed!
    pause
    exit /b 1
)
del tesseract.zip

cd tesseract
if not exist CMakeLists.txt (
    echo [ERROR] Tesseract CMakeLists.txt not found!
    pause
    exit /b 1
)

mkdir build
cd build

echo Configuring Tesseract with Leptonica path...
cmake .. -G "Visual Studio 17 2022" -A x64 ^
    -DCMAKE_BUILD_TYPE=Release ^
    -DCMAKE_INSTALL_PREFIX=%INSTALL_DIR%\tesseract ^
    -DBUILD_SHARED_LIBS=ON ^
    -DLeptonica_DIR=%INSTALL_DIR%\leptonica\lib\cmake\leptonica ^
    -DCMAKE_PREFIX_PATH=%INSTALL_DIR%\leptonica ^
    -DSW_BUILD=OFF ^
    -DBUILD_TRAINING_TOOLS=OFF ^
    -DBUILD_TESTS=OFF ^
    -DDISABLED_LEGACY_ENGINE=OFF ^
    -DOPENMP_BUILD=ON ^
    -DCMAKE_CXX_FLAGS_RELEASE="/O2 /Ob2 /Oi /Ot /Oy /GT /GL /GS- /fp:fast /arch:AVX512 /favor:AMD64 /openmp" ^
    -DCMAKE_C_FLAGS_RELEASE="/O2 /Ob2 /Oi /Ot /Oy /GT /GL /GS- /fp:fast /arch:AVX512 /favor:AMD64 /openmp"

if %ERRORLEVEL% NEQ 0 (
    echo [WARNING] First config attempt failed, trying explicit paths...
    cmake .. -G "Visual Studio 17 2022" -A x64 ^
        -DCMAKE_BUILD_TYPE=Release ^
        -DCMAKE_INSTALL_PREFIX=%INSTALL_DIR%\tesseract ^
        -DBUILD_SHARED_LIBS=ON ^
        -DLeptonica_INCLUDE_DIR=%INSTALL_DIR%\leptonica\include ^
        -DLeptonica_LIBRARY=%INSTALL_DIR%\leptonica\lib\leptonica-1.85.0.lib ^
        -DSW_BUILD=OFF ^
        -DBUILD_TRAINING_TOOLS=OFF ^
        -DBUILD_TESTS=OFF ^
        -DCMAKE_CXX_FLAGS_RELEASE="%ULTIMATE_FLAGS% /openmp" ^
        -DCMAKE_C_FLAGS_RELEASE="%ULTIMATE_FLAGS% /openmp"
    
    if %ERRORLEVEL% NEQ 0 (
        echo [ERROR] Tesseract configuration completely failed!
        pause
        exit /b 1
    )
)
echo [OK] Tesseract configured

echo Building Tesseract...
cmake --build . --config Release --target INSTALL -- /m:1

if %ERRORLEVEL% NEQ 0 (
    echo [ERROR] Tesseract build failed!
    pause
    exit /b 1
)

echo Verifying Tesseract installation...
if exist "%INSTALL_DIR%\tesseract\bin\tesseract55.dll" (
    echo [OK] tesseract55.dll found
) else if exist "%INSTALL_DIR%\tesseract\bin\tesseract*.dll" (
    echo [OK] Tesseract DLL found (different version)
    dir /B "%INSTALL_DIR%\tesseract\bin\tesseract*.dll"
) else (
    echo [ERROR] No Tesseract DLL found!
    pause
    exit /b 1
)

echo.
echo ============================================================
echo PART 3 COMPLETE - Tesseract verified and built
echo ============================================================
pause

REM ====================================================================
REM PART 4: TESSDATA WITH VERIFICATION
REM ====================================================================
echo.
echo ============================================================
echo PART 4: TESSDATA CONFIGURATION
echo ============================================================
echo.

cd /d %PROJECT_DIR%\bin
if not exist tessdata mkdir tessdata

echo Downloading English language data...
curl -L -o tessdata\eng.traineddata https://github.com/tesseract-ocr/tessdata_best/raw/main/eng.traineddata
if %ERRORLEVEL% NEQ 0 (
    echo [ERROR] Failed to download eng.traineddata!
    pause
    exit /b 1
)

echo Verifying eng.traineddata...
for %%F in (tessdata\eng.traineddata) do (
    if %%~zF LSS 10000000 (
        echo [ERROR] eng.traineddata too small!
        pause
        exit /b 1
    )
    echo [OK] eng.traineddata size: %%~zF bytes
)

echo Downloading OSD data...
curl -L -o tessdata\osd.traineddata https://github.com/tesseract-ocr/tessdata_best/raw/main/osd.traineddata

echo [OK] tessdata configured at: %PROJECT_DIR%\bin\tessdata\
pause

REM ====================================================================
REM PART 5: COPY ALL DLLS WITH VERIFICATION
REM ====================================================================
echo.
echo ============================================================
echo PART 5: DLL INSTALLATION AND VERIFICATION
echo ============================================================
echo.

cd /d %PROJECT_DIR%

echo Copying OpenCV DLLs...
xcopy /Y "%INSTALL_DIR%\opencv\x64\vc17\bin\*.dll" "bin\" >nul 2>&1
xcopy /Y "%INSTALL_DIR%\leptonica\bin\*.dll" "bin\" >nul 2>&1
xcopy /Y "%INSTALL_DIR%\tesseract\bin\*.dll" "bin\" >nul 2>&1

echo Copying libraries...
xcopy /Y "%INSTALL_DIR%\opencv\x64\vc17\lib\*.lib" "deps\lib\" >nul 2>&1
xcopy /Y "%INSTALL_DIR%\leptonica\lib\*.lib" "deps\lib\" >nul 2>&1
xcopy /Y "%INSTALL_DIR%\tesseract\lib\*.lib" "deps\lib\" >nul 2>&1

echo Copying headers...
xcopy /E /Y "%INSTALL_DIR%\opencv\include\*" "deps\include\" >nul 2>&1
xcopy /E /Y "%INSTALL_DIR%\leptonica\include\*" "deps\include\" >nul 2>&1
xcopy /E /Y "%INSTALL_DIR%\tesseract\include\*" "deps\include\" >nul 2>&1

echo.
echo Copying IMAGE FORMAT DLLs from vcpkg...
cd bin

set MISSING_DLLS=0
for %%D in (libpng16.dll jpeg62.dll tiff.dll zlib1.dll) do (
    if not exist "%%D" (
        if exist "%VCPKG_BIN%\%%D" (
            copy /Y "%VCPKG_BIN%\%%D" . >nul
            echo [OK] Copied %%D
        ) else (
            echo [WARNING] %%D not found in vcpkg!
            set /a MISSING_DLLS+=1
        )
    ) else (
        echo [OK] %%D already present
    )
)

if %MISSING_DLLS% GTR 0 (
    echo [WARNING] %MISSING_DLLS% image DLLs missing
    echo          OCR may fail on some image formats
)

echo.
echo Verifying critical DLLs...
set CRITICAL_OK=1
if not exist opencv_world*.dll (
    echo [ERROR] OpenCV world DLL missing!
    set CRITICAL_OK=0
)
if not exist tesseract*.dll (
    echo [ERROR] Tesseract DLL missing!
    set CRITICAL_OK=0
)
if not exist tessdata\eng.traineddata (
    echo [ERROR] tessdata missing!
    set CRITICAL_OK=0
)

if %CRITICAL_OK% EQU 0 (
    echo [ERROR] Critical DLLs missing!
    pause
    exit /b 1
)

echo [OK] All critical DLLs present
pause

REM ====================================================================
REM PART 6: BUILD ACCELERATOR WITH VERIFICATION
REM ====================================================================
echo.
echo ============================================================
echo PART 6: ACCELERATOR_SHAREDMEM.PYD BUILD
echo ============================================================
echo.

cd /d %PROJECT_DIR%

echo Verifying source file...
if not exist accelerator_sharedmem.cpp (
    echo [ERROR] accelerator_sharedmem.cpp NOT FOUND!
    echo        Cannot build without source file!
    pause
    exit /b 1
)
echo [OK] Source file found

echo Verifying Python...
if not exist "%PYTHON_HOME%\python.exe" (
    echo [ERROR] Python not found at %PYTHON_HOME%!
    pause
    exit /b 1
)

echo Installing Python dependencies...
%PYTHON_HOME%\python.exe -m pip install pybind11 numpy >nul 2>&1

echo Getting Python paths...
for /f "delims=" %%i in ('%PYTHON_HOME%\python.exe -c "import sysconfig; print(sysconfig.get_path('include'))"') do set PYTHON_INCLUDE=%%i
for /f "delims=" %%i in ('%PYTHON_HOME%\python.exe -c "import pybind11; print(pybind11.get_include())"') do set PYBIND11_INCLUDE=%%i
for /f "delims=" %%i in ('%PYTHON_HOME%\python.exe -c "import numpy; print(numpy.get_include())"') do set NUMPY_INCLUDE=%%i

if "%PYTHON_INCLUDE%"=="" (
    echo [ERROR] Failed to get Python paths!
    pause
    exit /b 1
)
echo [OK] Python paths configured

echo.
echo Compiling with ULTIMATE optimizations...
cl /MD %ULTIMATE_FLAGS% ^
    /I"%PYTHON_INCLUDE%" ^
    /I"%PYBIND11_INCLUDE%" ^
    /I"%NUMPY_INCLUDE%" ^
    /I"deps\include" ^
    /I"deps\opencv\include" ^
    /I"deps\tesseract\include" ^
    /I"deps\leptonica\include" ^
    /D_CRT_SECURE_NO_WARNINGS ^
    /DNDEBUG ^
    /c accelerator_sharedmem.cpp

if %ERRORLEVEL% NEQ 0 (
    echo [ERROR] Compilation failed!
    pause
    exit /b 1
)
echo [OK] Compilation successful

echo Linking...
link /DLL /OUT:bin\accelerator_sharedmem.pyd ^
    /LIBPATH:"%PYTHON_HOME%\libs" ^
    /LIBPATH:"deps\lib" ^
    /LIBPATH:"deps\opencv\x64\vc17\lib" ^
    /LIBPATH:"deps\tesseract\lib" ^
    /LIBPATH:"deps\leptonica\lib" ^
    accelerator_sharedmem.obj ^
    python311.lib ^
    opencv_world*.lib ^
    tesseract*.lib ^
    leptonica*.lib ^
    /LTCG /OPT:REF /OPT:ICF

if %ERRORLEVEL% NEQ 0 (
    echo [ERROR] Linking failed!
    pause
    exit /b 1
)

del accelerator_sharedmem.obj
echo [OK] accelerator_sharedmem.pyd created!

echo.
echo Verifying .pyd file...
if exist "bin\accelerator_sharedmem.pyd" (
    for %%F in (bin\accelerator_sharedmem.pyd) do (
        echo [OK] PYD created: %%~zF bytes
    )
) else (
    echo [ERROR] PYD file not created!
    pause
    exit /b 1
)

pause

REM ====================================================================
REM FINAL TEST WITH DETAILED VERIFICATION
REM ====================================================================
echo.
echo ============================================================
echo FINAL VERIFICATION TEST
echo ============================================================
echo.

cd bin

echo Test 1: Module import...
%PYTHON_HOME%\python.exe -c "import accelerator_sharedmem; print('[OK] Module imports')" 2>&1
if %ERRORLEVEL% NEQ 0 (
    echo [FAILED] Module won't import!
    echo.
    echo Attempting detailed diagnosis...
    %PYTHON_HOME%\python.exe -c "import ctypes; h = ctypes.CDLL('./accelerator_sharedmem.pyd')" 2>&1
    echo.
    echo Check for missing DLLs above
    pause
    exit /b 1
)

echo.
echo Test 2: Pipeline creation...
%PYTHON_HOME%\python.exe -c "import accelerator_sharedmem; p = accelerator_sharedmem.AcceleratorSharedMemPipeline(); print('[OK] Pipeline created')" 2>&1
if %ERRORLEVEL% NEQ 0 (
    echo [FAILED] Pipeline creation failed!
    echo         tessdata may be in wrong location
    pause
    exit /b 1
)

echo.
echo ============================================================
echo MISSION ACCOMPLISHED - BUILD VERIFIED AND COMPLETE!
echo ============================================================
echo.
echo All components built and verified:
echo ✓ OpenCV with AVX-512
echo ✓ Leptonica 
echo ✓ Tesseract with OpenMP
echo ✓ accelerator_sharedmem.pyd
echo ✓ tessdata in correct location
echo ✓ All tests passed
echo.
echo The 9950X3D BEAST is ready for maximum performance!
echo.
pause