@echo off
REM ====================================================================
REM BUILD TESSERACT + LEPTONICA FOR 9950X3D BEAST WITH ALL IMAGE DLLS
REM ====================================================================
REM Fixes:
REM 1. Correct Leptonica path for Tesseract to find it
REM 2. Optimizations for 9950X3D BEAST
REM 3. Copies all image format DLLs
REM ====================================================================

echo ============================================================
echo BUILD TESSERACT + LEPTONICA FOR AMD RYZEN 9 9950X3D
echo ============================================================
echo.
echo BUILD ENV: Single-core VM (slow compilation)
echo TARGET: AMD Ryzen 9 9950X3D BEAST (32 threads)
echo.
pause

set TESSERACT_VERSION=5.5.1
set LEPTONICA_VERSION=1.85.0
set BUILD_DIR=C:\TANK\ocr_accelerator\source
set INSTALL_DIR=C:\TANK\ocr_accelerator\deps
set VCPKG_BIN=C:\tools\vcpkg\installed\x64-windows\bin

cd /d %BUILD_DIR%

REM Clean old downloads
echo Cleaning old source directories...
if exist leptonica rmdir /s /q leptonica
if exist tesseract rmdir /s /q tesseract
echo [OK] Cleaned

REM ====================================================================
REM DOWNLOAD AND BUILD LEPTONICA WITH OPTIMIZATIONS
REM ====================================================================
echo.
echo ============================================================
echo STEP 1: BUILDING LEPTONICA %LEPTONICA_VERSION%
echo ============================================================
echo.

echo Downloading Leptonica...
curl -L -o leptonica.zip https://github.com/DanBloomberg/leptonica/archive/refs/tags/%LEPTONICA_VERSION%.zip

echo Extracting...
tar -xf leptonica.zip
move leptonica-%LEPTONICA_VERSION% leptonica
del leptonica.zip

cd leptonica
mkdir build
cd build

echo.
echo Configuring Leptonica for 9950X3D target...
cmake .. -G "Visual Studio 17 2022" -A x64 ^
    -DCMAKE_BUILD_TYPE=Release ^
    -DCMAKE_INSTALL_PREFIX=%INSTALL_DIR%\leptonica ^
    -DBUILD_SHARED_LIBS=ON ^
    -DSW_BUILD=OFF ^
    -DCMAKE_CXX_FLAGS_RELEASE="/O2 /Ob2 /Oi /Ot /Oy /GT /GL /GS- /fp:fast /arch:AVX2" ^
    -DCMAKE_C_FLAGS_RELEASE="/O2 /Ob2 /Oi /Ot /Oy /GT /GL /GS- /fp:fast /arch:AVX2"

if %ERRORLEVEL% NEQ 0 (
    echo ERROR: Leptonica CMake failed!
    pause
    exit /b 1
)

echo Building Leptonica (single thread, ~30 minutes)...
cmake --build . --config Release --target INSTALL -- /m:1

if %ERRORLEVEL% NEQ 0 (
    echo ERROR: Leptonica build failed!
    pause
    exit /b 1
)

echo [OK] Leptonica built and installed

REM ====================================================================
REM FIX: CREATE CMAKE CONFIG IF MISSING
REM ====================================================================
echo.
echo Creating Leptonica CMake config for Tesseract to find...
if not exist "%INSTALL_DIR%\leptonica\lib\cmake\leptonica" (
    mkdir "%INSTALL_DIR%\leptonica\lib\cmake\leptonica"
)

REM Create LeptonicaConfig.cmake if it doesn't exist
(
echo # Leptonica CMake Config
echo set(Leptonica_INCLUDE_DIRS "%INSTALL_DIR%\leptonica\include"^)
echo set(Leptonica_LIBRARIES "%INSTALL_DIR%\leptonica\lib\leptonica-1.85.0.lib"^)
echo set(Leptonica_FOUND TRUE^)
) > "%INSTALL_DIR%\leptonica\lib\cmake\leptonica\LeptonicaConfig.cmake"

REM ====================================================================
REM DOWNLOAD AND BUILD TESSERACT WITH OPTIMIZATIONS
REM ====================================================================
cd /d %BUILD_DIR%

echo.
echo ============================================================
echo STEP 2: BUILDING TESSERACT %TESSERACT_VERSION%
echo ============================================================
echo.

echo Downloading Tesseract...
curl -L -o tesseract.zip https://github.com/tesseract-ocr/tesseract/archive/refs/tags/%TESSERACT_VERSION%.zip

echo Extracting...
tar -xf tesseract.zip
move tesseract-%TESSERACT_VERSION% tesseract
del tesseract.zip

cd tesseract
mkdir build
cd build

echo.
echo Configuring Tesseract for 9950X3D target...
echo Using Leptonica from: %INSTALL_DIR%\leptonica
echo.

cmake .. -G "Visual Studio 17 2022" -A x64 ^
    -DCMAKE_BUILD_TYPE=Release ^
    -DCMAKE_INSTALL_PREFIX=%INSTALL_DIR%\tesseract ^
    -DBUILD_SHARED_LIBS=ON ^
    -DLeptonica_DIR=%INSTALL_DIR%\leptonica\lib\cmake\leptonica ^
    -DCMAKE_PREFIX_PATH=%INSTALL_DIR%\leptonica ^
    -DSW_BUILD=OFF ^
    -DBUILD_TRAINING_TOOLS=OFF ^
    -DBUILD_TESTS=OFF ^
    -DGRAPHICS_DISABLED=OFF ^
    -DDISABLED_LEGACY_ENGINE=OFF ^
    -DENABLE_LTO=OFF ^
    -DOPENMP_BUILD=ON ^
    -DCMAKE_CXX_FLAGS_RELEASE="/O2 /Ob2 /Oi /Ot /Oy /GT /GL /GS- /fp:fast /arch:AVX2 /openmp" ^
    -DCMAKE_C_FLAGS_RELEASE="/O2 /Ob2 /Oi /Ot /Oy /GT /GL /GS- /fp:fast /arch:AVX2 /openmp"

if %ERRORLEVEL% NEQ 0 (
    echo.
    echo Tesseract couldn't find Leptonica, trying alternate method...
    cmake .. -G "Visual Studio 17 2022" -A x64 ^
        -DCMAKE_BUILD_TYPE=Release ^
        -DCMAKE_INSTALL_PREFIX=%INSTALL_DIR%\tesseract ^
        -DBUILD_SHARED_LIBS=ON ^
        -DLeptonica_INCLUDE_DIR=%INSTALL_DIR%\leptonica\include ^
        -DLeptonica_LIBRARY=%INSTALL_DIR%\leptonica\lib\leptonica-1.85.0.lib ^
        -DSW_BUILD=OFF ^
        -DBUILD_TRAINING_TOOLS=OFF ^
        -DBUILD_TESTS=OFF
        
    if %ERRORLEVEL% NEQ 0 (
        echo ERROR: Tesseract CMake failed even with explicit paths!
        pause
        exit /b 1
    )
)

echo Building Tesseract (single thread, ~45 minutes)...
cmake --build . --config Release --target INSTALL -- /m:1

if %ERRORLEVEL% NEQ 0 (
    echo ERROR: Tesseract build failed!
    pause
    exit /b 1
)

echo [OK] Tesseract built

REM ====================================================================
REM DOWNLOAD LANGUAGE DATA
REM ====================================================================
echo.
echo ============================================================
echo STEP 3: DOWNLOADING LANGUAGE DATA
echo ============================================================
echo.

cd /d %INSTALL_DIR%\tesseract\bin
if not exist tessdata mkdir tessdata

echo Downloading English trained data (best quality)...
curl -L -o tessdata\eng.traineddata https://github.com/tesseract-ocr/tessdata_best/raw/main/eng.traineddata
echo Downloading orientation detection data...
curl -L -o tessdata\osd.traineddata https://github.com/tesseract-ocr/tessdata_best/raw/main/osd.traineddata

REM ====================================================================
REM COPY TO BIN INCLUDING IMAGE FORMAT DLLS
REM ====================================================================
echo.
echo ============================================================
echo STEP 4: INSTALLING DLLS AND IMAGE LIBRARIES
echo ============================================================
echo.

cd /d C:\TANK\ocr_accelerator

echo Copying Tesseract/Leptonica DLLs...
xcopy /Y "%INSTALL_DIR%\leptonica\bin\*.dll" "bin\" >nul 2>&1
xcopy /Y "%INSTALL_DIR%\tesseract\bin\*.dll" "bin\" >nul 2>&1

echo Copying libraries...
xcopy /Y "%INSTALL_DIR%\leptonica\lib\*.lib" "deps\lib\" >nul 2>&1
xcopy /Y "%INSTALL_DIR%\tesseract\lib\*.lib" "deps\lib\" >nul 2>&1

echo Copying headers...
xcopy /E /Y "%INSTALL_DIR%\leptonica\include\*" "deps\include\" >nul 2>&1
xcopy /E /Y "%INSTALL_DIR%\tesseract\include\*" "deps\include\" >nul 2>&1

echo Copying tessdata...
xcopy /E /Y "%INSTALL_DIR%\tesseract\bin\tessdata\*" "bin\tessdata\" >nul 2>&1

echo.
echo Copying IMAGE FORMAT DLLs from vcpkg (CRITICAL!)...
cd bin

REM Core image libraries needed for OCR
for %%D in (libpng16.dll jpeg62.dll turbojpeg.dll tiff.dll gif.dll openjp2.dll libwebp.dll libwebpdecoder.dll libwebpdemux.dll libwebpmux.dll zlib1.dll) do (
    if exist "%VCPKG_BIN%\%%D" (
        copy /Y "%VCPKG_BIN%\%%D" . >nul
        echo [COPIED] %%D
    ) else (
        echo [MISSING] %%D - checking variants...
    )
)

REM Additional dependencies
for %%D in (liblzma.dll bz2.dll libcrypto-3-x64.dll libssl-3-x64.dll libcurl.dll sqlite3.dll libexpat.dll libsharpyuv.dll archive.dll lz4.dll zstd.dll) do (
    if exist "%VCPKG_BIN%\%%D" (
        copy /Y "%VCPKG_BIN%\%%D" . >nul
        echo [COPIED] %%D
    )
)

REM Copy with wildcards for version variations
xcopy /Y "%VCPKG_BIN%\png*.dll" . >nul 2>&1
xcopy /Y "%VCPKG_BIN%\jpeg*.dll" . >nul 2>&1
xcopy /Y "%VCPKG_BIN%\tiff*.dll" . >nul 2>&1
xcopy /Y "%VCPKG_BIN%\zlib*.dll" . >nul 2>&1

REM ====================================================================
REM VERIFY BUILD
REM ====================================================================
echo.
echo ============================================================
echo STEP 5: VERIFYING BUILD
echo ============================================================
echo.

cd /d C:\TANK\ocr_accelerator\bin

if exist tesseract55.dll (
    echo [OK] tesseract55.dll found
) else (
    if exist tesseract*.dll (
        dir /B tesseract*.dll
    ) else (
        echo [ERROR] Tesseract DLL not found!
    )
)

if exist leptonica-1.85.0.dll (
    echo [OK] leptonica-1.85.0.dll found
) else (
    if exist leptonica*.dll (
        dir /B leptonica*.dll
    ) else if exist lept*.dll (
        dir /B lept*.dll
    ) else (
        echo [ERROR] Leptonica DLL not found!
    )
)

echo.
echo Image format DLLs present:
dir /B *.dll | findstr /i "png jpeg jpg tiff gif webp" 2>nul

echo.
echo ============================================================
echo BUILD COMPLETE - OPTIMIZED FOR 9950X3D!
echo ============================================================
echo.
echo Built with optimizations:
echo - AVX2 + FMA instructions
echo - OpenMP (will use 32 threads on BEAST)
echo - Fast math enabled
echo - All image format DLLs included
echo.
echo Components:
echo - Leptonica %LEPTONICA_VERSION% (image processing)
echo - Tesseract %TESSERACT_VERSION% (OCR engine)
echo - Image libraries (PNG, JPEG, TIFF, etc.)
echo - Language data (English best quality)
echo.
echo Ready for 50ms OCR on the BEAST!
echo.
pause