@echo off
REM ============================================================================
REM TESTRADE Mock Broker Service
REM 
REM Starts the local mock broker for development and TANK mode testing
REM ============================================================================

title TESTRADE Mock Broker

echo.
echo ============================================================================
echo                    TESTRADE MOCK BROKER SERVICE
echo                  Local Broker for Development/Testing
echo ============================================================================
echo Timestamp: %date% %time%
echo.

REM Change to TESTRADE directory
cd /d "C:\TANK"
echo Current directory: %CD%

REM Check if virtual environment exists
if exist "venv\Scripts\activate.bat" (
    echo [INFO] Found virtual environment at venv\Scripts\activate.bat
    echo [INFO] Activating virtual environment...
    call venv\Scripts\activate.bat
) else if exist ".venv\Scripts\activate.bat" (
    echo [INFO] Found virtual environment at .venv\Scripts\activate.bat
    echo [INFO] Activating virtual environment...
    call .venv\Scripts\activate.bat
) else (
    echo [ERROR] Virtual environment not found at venv\ or .venv\
    echo Please ensure the virtual environment is set up correctly.
    echo To create a virtual environment, run:
    echo   python -m venv venv
    echo   venv\Scripts\activate
    echo   pip install -r requirements.txt
    echo.
    pause
    exit /b 1
)

REM Verify Python is available
python --version >nul 2>&1
if errorlevel 1 (
    echo [ERROR] Python not available after activating virtual environment
    pause
    exit /b 1
)

REM Check if mock broker exists
if not exist "intellisense\mock_broker\mock_broker_server.py" (
    echo [ERROR] Mock broker server not found at intellisense\mock_broker\mock_broker_server.py
    echo Please ensure the mock broker files are in place.
    echo.
    pause
    exit /b 1
)

echo.
echo ========================================
echo Starting Mock Broker Service
echo ========================================
echo.

REM Set PYTHONPATH to include TANK root
set PYTHONPATH=C:\TANK;%PYTHONPATH%
echo [INFO] PYTHONPATH set to: %PYTHONPATH%

REM Start Mock Broker Server
echo [INFO] Starting Mock Broker Server on localhost...
echo [INFO] This provides local broker functionality for TANK mode
echo.

python intellisense\mock_broker\mock_broker_server.py

REM Store exit code
set EXIT_CODE=%errorlevel%

echo.
echo ============================================================================
if %EXIT_CODE% neq 0 (
    echo MOCK BROKER FAILED (Exit Code: %EXIT_CODE%)
) else (
    echo MOCK BROKER SHUTDOWN COMPLETE
)
echo ============================================================================
echo.

echo Press any key to exit...
pause >nul