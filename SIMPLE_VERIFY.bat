@echo off
REM ====================================================================
REM SIMPLE PRE-BUILD VERIFICATION - NO FALSE POSITIVES
REM ====================================================================

echo ============================================================
echo SIMPLE BUILD VERIFICATION
echo ============================================================
echo.

set READY=YES

REM ====================================================================
REM 1. CHECK COMPILER
REM ====================================================================
echo [1] Checking for C++ compiler (cl.exe)...
where cl.exe >nul 2>&1
if %ERRORLEVEL% EQU 0 (
    echo     OK - cl.exe found
    cl.exe 2>&1 | findstr /C:"Version" | findstr /C:"19."
) else (
    echo     MISSING - cl.exe not found!
    echo     ACTION: Run from "x64 Native Tools Command Prompt for VS 2022"
    set READY=NO
)
echo.

REM ====================================================================
REM 2. CHECK CMAKE
REM ====================================================================
echo [2] Checking for CMake...
where cmake.exe >nul 2>&1
if %ERRORLEVEL% EQU 0 (
    echo     OK - CMake found
    cmake --version | findstr /C:"version"
) else (
    echo     MISSING - CMake not found!
    echo     ACTION: Download from cmake.org
    set READY=NO
)
echo.

REM ====================================================================
REM 3. CHECK PYTHON
REM ====================================================================
echo [3] Checking for Python...
set PYTHON_OK=NO

REM Check C:\Python311 first
if exist "C:\Python311\python.exe" (
    C:\Python311\python.exe --version 2>&1 | findstr /C:"3.11"
    if %ERRORLEVEL% EQU 0 (
        echo     OK - Python 3.11 found at C:\Python311
        set PYTHON_OK=YES
    )
)

REM Check PATH if not found
if "%PYTHON_OK%"=="NO" (
    where python.exe >nul 2>&1
    if %ERRORLEVEL% EQU 0 (
        python --version 2>&1 | findstr /C:"3.11"
        if %ERRORLEVEL% EQU 0 (
            echo     OK - Python 3.11 found in PATH
            set PYTHON_OK=YES
        ) else (
            echo     WARNING - Python found but not 3.11
            python --version
        )
    ) else (
        echo     MISSING - Python not found!
        echo     ACTION: Install Python 3.11 from python.org
        set READY=NO
    )
)
echo.

REM ====================================================================
REM 4. CHECK DIRECTORIES
REM ====================================================================
echo [4] Checking project directories...
if not exist "C:\TANK\ocr_accelerator" (
    echo     Creating C:\TANK\ocr_accelerator...
    mkdir "C:\TANK\ocr_accelerator" 2>nul
    mkdir "C:\TANK\ocr_accelerator\source" 2>nul
    mkdir "C:\TANK\ocr_accelerator\deps" 2>nul
    mkdir "C:\TANK\ocr_accelerator\bin" 2>nul
)
echo     OK - Directories ready
echo.

REM ====================================================================
REM 5. CHECK SOURCE FILE
REM ====================================================================
echo [5] Checking for source code...
if exist "C:\TANK\ocr_accelerator\accelerator_sharedmem.cpp" (
    echo     OK - accelerator_sharedmem.cpp found
) else (
    echo     MISSING - accelerator_sharedmem.cpp not found
    echo     ACTION: Copy from backup or GitHub
)
echo.

REM ====================================================================
REM 6. CHECK DOWNLOADS
REM ====================================================================
echo [6] Checking download tools...
where curl.exe >nul 2>&1
if %ERRORLEVEL% EQU 0 (
    echo     OK - curl found
) else (
    echo     MISSING - curl not found
    set READY=NO
)

where tar.exe >nul 2>&1
if %ERRORLEVEL% EQU 0 (
    echo     OK - tar found
) else (
    echo     MISSING - tar not found
    set READY=NO
)
echo.

REM ====================================================================
REM 7. OPTIONAL CHECKS
REM ====================================================================
echo [7] Optional components...

REM Check vcpkg
if exist "C:\tools\vcpkg\vcpkg.exe" (
    echo     OK - vcpkg found at C:\tools\vcpkg
) else if exist "C:\vcpkg\vcpkg.exe" (
    echo     OK - vcpkg found at C:\vcpkg
) else (
    echo     INFO - vcpkg not found (optional)
)

REM Check for VS environment
if defined INCLUDE (
    echo     OK - VS environment variables set
) else (
    echo     INFO - VS environment may not be fully configured
)
echo.

REM ====================================================================
REM SUMMARY
REM ====================================================================
echo ============================================================
if "%READY%"=="YES" (
    echo RESULT: READY TO BUILD
    echo ============================================================
    echo.
    echo All essential components found!
    echo You can proceed with the build.
) else (
    echo RESULT: NOT READY
    echo ============================================================
    echo.
    echo Fix the MISSING items above before building.
    echo.
    echo Most likely fix:
    echo   Run this from "x64 Native Tools Command Prompt for VS 2022"
)
echo ============================================================
echo.
pause