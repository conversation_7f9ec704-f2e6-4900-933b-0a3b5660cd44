@echo off
REM ====================================================================
REM COMPLETE OCR BUILD WITH AVX-512 FOR AMD RYZEN 9 9950X3D
REM ====================================================================
REM Uses CORRECT /arch:AVX512 flag for Visual Studio 2022
REM ====================================================================

echo ============================================================
echo COMPLETE OCR BUILD WITH AVX-512 SUPPORT
echo ============================================================
echo.
echo Visual Studio 2022 DOES support /arch:AVX512!
echo.
echo Target CPU: AMD Ryzen 9 9950X3D
echo - Zen 4 architecture with AVX-512 support
echo - F, CD, BW, DQ, VL extensions
echo.
echo This build uses:
echo - /arch:AVX512 compiler flag (correct for VS2022)
echo - CPU_DISPATCH for OpenCV runtime detection
echo - Single thread compilation (VM limitation)
echo.
pause

set OPENCV_VERSION=4.10.0
set TESSERACT_VERSION=5.5.1
set LEPTONICA_VERSION=1.85.0
set BUILD_DIR=C:\TANK\ocr_accelerator\source
set INSTALL_DIR=C:\TANK\ocr_accelerator\deps
set PROJECT_DIR=C:\TANK\ocr_accelerator
set ARTIFACTS_DIR=C:\TANK\ocr_accelerator\build_artifacts
set VCPKG_BIN=C:\tools\vcpkg\installed\x64-windows\bin
set PYTHON_HOME=C:\Python311

REM Create directories
if not exist %BUILD_DIR% mkdir %BUILD_DIR%
if not exist %INSTALL_DIR% mkdir %INSTALL_DIR%
if not exist %ARTIFACTS_DIR% mkdir %ARTIFACTS_DIR%
if not exist %ARTIFACTS_DIR%\logs mkdir %ARTIFACTS_DIR%\logs
if not exist %PROJECT_DIR%\bin mkdir %PROJECT_DIR%\bin
if not exist %PROJECT_DIR%\deps\lib mkdir %PROJECT_DIR%\deps\lib
if not exist %PROJECT_DIR%\deps\include mkdir %PROJECT_DIR%\deps\include

REM ====================================================================
REM PART 1: BUILD OPENCV WITH AVX-512
REM ====================================================================
echo.
echo ============================================================
echo PART 1: BUILDING OPENCV WITH AVX-512
echo ============================================================
echo.
echo Using proper Visual Studio 2022 AVX-512 support
echo.
pause

cd /d %BUILD_DIR%

if exist opencv\build\OpenCV.sln (
    echo OpenCV.sln already exists
    choice /C YN /T 10 /D N /M "Skip OpenCV build"
    if errorlevel 2 goto PART2_LEPTONICA
)

if not exist opencv\CMakeLists.txt (
    echo Downloading OpenCV %OPENCV_VERSION%...
    curl -L -o opencv.zip https://github.com/opencv/opencv/archive/refs/tags/%OPENCV_VERSION%.zip
    
    echo Extracting OpenCV...
    tar -xf opencv.zip
    move opencv-%OPENCV_VERSION% opencv
    del opencv.zip

    echo Downloading OpenCV contrib...
    curl -L -o opencv_contrib.zip https://github.com/opencv/opencv_contrib/archive/refs/tags/%OPENCV_VERSION%.zip
    tar -xf opencv_contrib.zip
    move opencv_contrib-%OPENCV_VERSION% opencv_contrib
    del opencv_contrib.zip
)

cd opencv
if not exist build mkdir build
cd build

echo.
echo Configuring OpenCV with AVX-512 support...
echo Using both CPU_DISPATCH and compiler flags...
cmake .. -G "Visual Studio 17 2022" -A x64 ^
    -DCMAKE_BUILD_TYPE=Release ^
    -DCMAKE_INSTALL_PREFIX=%INSTALL_DIR%\opencv ^
    -DBUILD_opencv_world=ON ^
    -DBUILD_SHARED_LIBS=ON ^
    -DWITH_TBB=OFF ^
    -DWITH_OPENMP=ON ^
    -DWITH_IPP=OFF ^
    -DCPU_BASELINE=AVX2 ^
    -DCPU_DISPATCH=AVX2,FMA3,AVX_512F,AVX512_COMMON,AVX512_KNL,AVX512_SKX,AVX512_CLX,AVX512_CNL,AVX512_ICL ^
    -DENABLE_FAST_MATH=ON ^
    -DCV_ENABLE_INTRINSICS=ON ^
    -DBUILD_TESTS=OFF ^
    -DBUILD_PERF_TESTS=OFF ^
    -DBUILD_EXAMPLES=OFF ^
    -DBUILD_DOCS=OFF ^
    -DBUILD_opencv_apps=OFF ^
    -DBUILD_opencv_python2=OFF ^
    -DBUILD_opencv_python3=OFF ^
    -DOPENCV_EXTRA_MODULES_PATH=../../opencv_contrib/modules ^
    -DWITH_CUDA=OFF ^
    -DWITH_OPENCL=OFF ^
    -DWITH_PNG=ON ^
    -DWITH_JPEG=ON ^
    -DWITH_TIFF=ON ^
    -DWITH_WEBP=ON ^
    -DENABLE_PRECOMPILED_HEADERS=OFF ^
    -DCMAKE_CXX_FLAGS_RELEASE="/O2 /Ob2 /Oi /Ot /Oy /GT /GL /GS- /fp:fast /arch:AVX512 /D__AVX512F__ /D__AVX512CD__ /D__AVX512BW__ /D__AVX512DQ__ /D__AVX512VL__" ^
    -DCMAKE_C_FLAGS_RELEASE="/O2 /Ob2 /Oi /Ot /Oy /GT /GL /GS- /fp:fast /arch:AVX512"

if %ERRORLEVEL% NEQ 0 (
    echo ERROR: OpenCV CMake configuration failed!
    echo.
    echo Trying without explicit AVX512 preprocessor defines...
    cmake .. -G "Visual Studio 17 2022" -A x64 ^
        -DCMAKE_BUILD_TYPE=Release ^
        -DCMAKE_INSTALL_PREFIX=%INSTALL_DIR%\opencv ^
        -DBUILD_opencv_world=ON ^
        -DBUILD_SHARED_LIBS=ON ^
        -DWITH_OPENMP=ON ^
        -DCPU_BASELINE=AVX2 ^
        -DCPU_DISPATCH=AVX2,FMA3,AVX_512F,AVX512_COMMON,AVX512_SKX ^
        -DENABLE_FAST_MATH=ON ^
        -DBUILD_TESTS=OFF ^
        -DBUILD_PERF_TESTS=OFF ^
        -DBUILD_EXAMPLES=OFF ^
        -DOPENCV_EXTRA_MODULES_PATH=../../opencv_contrib/modules ^
        -DCMAKE_CXX_FLAGS_RELEASE="/O2 /arch:AVX512" ^
        -DCMAKE_C_FLAGS_RELEASE="/O2 /arch:AVX512"
    
    if %ERRORLEVEL% NEQ 0 (
        echo ERROR: OpenCV configuration completely failed!
        pause
        exit /b 1
    )
)

echo [OK] OpenCV configured with AVX-512 support
if exist OpenCV.sln copy OpenCV.sln %ARTIFACTS_DIR%\

echo.
echo Building OpenCV with SINGLE thread (VM limitation)...
echo AVX-512 optimizations will activate on the 9950X3D
echo Started at: %time%
cmake --build . --config Release --target INSTALL -- /m:1

if %ERRORLEVEL% NEQ 0 (
    echo ERROR: OpenCV build failed!
    pause
    exit /b 1
)

echo Completed at: %time%
echo [OK] OpenCV built with AVX-512!

echo Installing OpenCV files...
cd /d %PROJECT_DIR%
xcopy /Y "%INSTALL_DIR%\opencv\x64\vc17\bin\*.dll" "bin\" >nul 2>&1
xcopy /Y "%INSTALL_DIR%\opencv\x64\vc17\lib\*.lib" "deps\lib\" >nul 2>&1
xcopy /E /Y "%INSTALL_DIR%\opencv\include\*" "deps\include\" >nul 2>&1

echo.
echo ============================================================
echo PART 1 COMPLETE - OpenCV built with AVX-512
echo ============================================================
pause

:PART2_LEPTONICA
REM ====================================================================
REM PART 2: BUILD LEPTONICA WITH AVX-512
REM ====================================================================
echo.
echo ============================================================
echo PART 2: BUILDING LEPTONICA WITH AVX-512
echo ============================================================
echo.
pause

cd /d %BUILD_DIR%

if exist leptonica rmdir /s /q leptonica

echo Downloading Leptonica %LEPTONICA_VERSION%...
curl -L -o leptonica.zip https://github.com/DanBloomberg/leptonica/archive/refs/tags/%LEPTONICA_VERSION%.zip

echo Extracting...
tar -xf leptonica.zip
move leptonica-%LEPTONICA_VERSION% leptonica
del leptonica.zip

cd leptonica
mkdir build
cd build

echo Configuring Leptonica with AVX-512...
cmake .. -G "Visual Studio 17 2022" -A x64 ^
    -DCMAKE_BUILD_TYPE=Release ^
    -DCMAKE_INSTALL_PREFIX=%INSTALL_DIR%\leptonica ^
    -DBUILD_SHARED_LIBS=ON ^
    -DSW_BUILD=OFF ^
    -DCMAKE_CXX_FLAGS_RELEASE="/O2 /Ob2 /Oi /Ot /Oy /GT /GL /GS- /fp:fast /arch:AVX512" ^
    -DCMAKE_C_FLAGS_RELEASE="/O2 /Ob2 /Oi /Ot /Oy /GT /GL /GS- /fp:fast /arch:AVX512"

if %ERRORLEVEL% NEQ 0 (
    echo ERROR: Leptonica CMake failed!
    pause
    exit /b 1
)

echo Building Leptonica (single thread)...
cmake --build . --config Release --target INSTALL -- /m:1

if %ERRORLEVEL% NEQ 0 (
    echo ERROR: Leptonica build failed!
    pause
    exit /b 1
)

echo [OK] Leptonica built with AVX-512!

REM Create CMake config for Tesseract
if not exist "%INSTALL_DIR%\leptonica\lib\cmake\leptonica" (
    mkdir "%INSTALL_DIR%\leptonica\lib\cmake\leptonica"
)

(
echo # Leptonica CMake Config
echo set(Leptonica_INCLUDE_DIRS "%INSTALL_DIR%\leptonica\include"^)
echo set(Leptonica_LIBRARIES "%INSTALL_DIR%\leptonica\lib\leptonica-1.85.0.lib"^)
echo set(Leptonica_FOUND TRUE^)
) > "%INSTALL_DIR%\leptonica\lib\cmake\leptonica\LeptonicaConfig.cmake"

echo.
echo ============================================================
echo PART 2 COMPLETE - Leptonica built with AVX-512
echo ============================================================
pause

REM ====================================================================
REM PART 3: BUILD TESSERACT WITH AVX-512
REM ====================================================================
echo.
echo ============================================================
echo PART 3: BUILDING TESSERACT WITH AVX-512
echo ============================================================
echo.
pause

cd /d %BUILD_DIR%

if exist tesseract rmdir /s /q tesseract

echo Downloading Tesseract %TESSERACT_VERSION%...
curl -L -o tesseract.zip https://github.com/tesseract-ocr/tesseract/archive/refs/tags/%TESSERACT_VERSION%.zip

echo Extracting...
tar -xf tesseract.zip
move tesseract-%TESSERACT_VERSION% tesseract
del tesseract.zip

cd tesseract
mkdir build
cd build

echo Configuring Tesseract with AVX-512 and OpenMP...
cmake .. -G "Visual Studio 17 2022" -A x64 ^
    -DCMAKE_BUILD_TYPE=Release ^
    -DCMAKE_INSTALL_PREFIX=%INSTALL_DIR%\tesseract ^
    -DBUILD_SHARED_LIBS=ON ^
    -DLeptonica_DIR=%INSTALL_DIR%\leptonica\lib\cmake\leptonica ^
    -DCMAKE_PREFIX_PATH=%INSTALL_DIR%\leptonica ^
    -DSW_BUILD=OFF ^
    -DBUILD_TRAINING_TOOLS=OFF ^
    -DBUILD_TESTS=OFF ^
    -DDISABLED_LEGACY_ENGINE=OFF ^
    -DOPENMP_BUILD=ON ^
    -DCMAKE_CXX_FLAGS_RELEASE="/O2 /Ob2 /Oi /Ot /Oy /GT /GL /GS- /fp:fast /arch:AVX512 /openmp" ^
    -DCMAKE_C_FLAGS_RELEASE="/O2 /Ob2 /Oi /Ot /Oy /GT /GL /GS- /fp:fast /arch:AVX512 /openmp"

if %ERRORLEVEL% NEQ 0 (
    echo Trying with explicit Leptonica paths...
    cmake .. -G "Visual Studio 17 2022" -A x64 ^
        -DCMAKE_BUILD_TYPE=Release ^
        -DCMAKE_INSTALL_PREFIX=%INSTALL_DIR%\tesseract ^
        -DBUILD_SHARED_LIBS=ON ^
        -DLeptonica_INCLUDE_DIR=%INSTALL_DIR%\leptonica\include ^
        -DLeptonica_LIBRARY=%INSTALL_DIR%\leptonica\lib\leptonica-1.85.0.lib ^
        -DSW_BUILD=OFF ^
        -DBUILD_TRAINING_TOOLS=OFF ^
        -DBUILD_TESTS=OFF ^
        -DCMAKE_CXX_FLAGS_RELEASE="/O2 /arch:AVX512 /openmp" ^
        -DCMAKE_C_FLAGS_RELEASE="/O2 /arch:AVX512 /openmp"
    
    if %ERRORLEVEL% NEQ 0 (
        echo ERROR: Tesseract configuration failed!
        pause
        exit /b 1
    )
)

echo Building Tesseract (single thread)...
cmake --build . --config Release --target INSTALL -- /m:1

if %ERRORLEVEL% NEQ 0 (
    echo ERROR: Tesseract build failed!
    pause
    exit /b 1
)

echo [OK] Tesseract built with AVX-512!

echo.
echo ============================================================
echo PART 3 COMPLETE - Tesseract built with AVX-512
echo ============================================================
pause

REM ====================================================================
REM PART 4: SETUP TESSDATA
REM ====================================================================
echo.
echo ============================================================
echo PART 4: SETTING UP TESSDATA
echo ============================================================
echo.
pause

cd /d %PROJECT_DIR%\bin

if not exist tessdata mkdir tessdata

echo Downloading English language data...
curl -L -o tessdata\eng.traineddata https://github.com/tesseract-ocr/tessdata_best/raw/main/eng.traineddata

echo Downloading orientation detection data...
curl -L -o tessdata\osd.traineddata https://github.com/tesseract-ocr/tessdata_best/raw/main/osd.traineddata

echo [OK] tessdata configured at: %PROJECT_DIR%\bin\tessdata\

echo.
echo ============================================================
echo PART 4 COMPLETE - tessdata ready
echo ============================================================
pause

REM ====================================================================
REM PART 5: COPY ALL DLLS
REM ====================================================================
echo.
echo ============================================================
echo PART 5: INSTALLING ALL DLLS
echo ============================================================
echo.
pause

cd /d %PROJECT_DIR%

echo Copying OpenCV DLLs...
xcopy /Y "%INSTALL_DIR%\opencv\x64\vc17\bin\*.dll" "bin\" >nul 2>&1

echo Copying Leptonica DLLs...
xcopy /Y "%INSTALL_DIR%\leptonica\bin\*.dll" "bin\" >nul 2>&1

echo Copying Tesseract DLLs...
xcopy /Y "%INSTALL_DIR%\tesseract\bin\*.dll" "bin\" >nul 2>&1

echo Copying libraries...
xcopy /Y "%INSTALL_DIR%\opencv\x64\vc17\lib\*.lib" "deps\lib\" >nul 2>&1
xcopy /Y "%INSTALL_DIR%\leptonica\lib\*.lib" "deps\lib\" >nul 2>&1
xcopy /Y "%INSTALL_DIR%\tesseract\lib\*.lib" "deps\lib\" >nul 2>&1

echo Copying headers...
xcopy /E /Y "%INSTALL_DIR%\opencv\include\*" "deps\include\" >nul 2>&1
xcopy /E /Y "%INSTALL_DIR%\leptonica\include\*" "deps\include\" >nul 2>&1
xcopy /E /Y "%INSTALL_DIR%\tesseract\include\*" "deps\include\" >nul 2>&1

echo.
echo Copying IMAGE FORMAT DLLs from vcpkg...
cd bin

set ESSENTIAL_DLLS=libpng16.dll jpeg62.dll tiff.dll libwebp.dll zlib1.dll

for %%D in (%ESSENTIAL_DLLS%) do (
    if not exist "%%D" (
        if exist "%VCPKG_BIN%\%%D" (
            copy /Y "%VCPKG_BIN%\%%D" . >nul
            echo [COPIED] %%D
        )
    )
)

REM Additional DLLs
for %%D in (turbojpeg.dll gif.dll openjp2.dll libwebpdecoder.dll libwebpdemux.dll libwebpmux.dll liblzma.dll bz2.dll libsharpyuv.dll archive.dll lz4.dll zstd.dll) do (
    if exist "%VCPKG_BIN%\%%D" (
        copy /Y "%VCPKG_BIN%\%%D" . >nul 2>&1
    )
)

REM Try alternate names
xcopy /Y "%VCPKG_BIN%\png*.dll" . >nul 2>&1
xcopy /Y "%VCPKG_BIN%\jpeg*.dll" . >nul 2>&1
xcopy /Y "%VCPKG_BIN%\tiff*.dll" . >nul 2>&1
xcopy /Y "%VCPKG_BIN%\zlib*.dll" . >nul 2>&1

echo.
echo ============================================================
echo PART 5 COMPLETE - All DLLs installed
echo ============================================================
pause

REM ====================================================================
REM PART 6: BUILD ACCELERATOR WITH AVX-512
REM ====================================================================
echo.
echo ============================================================
echo PART 6: BUILDING ACCELERATOR_SHAREDMEM.PYD WITH AVX-512
echo ============================================================
echo.
echo Using /arch:AVX512 for maximum performance on 9950X3D
echo.
pause

cd /d %PROJECT_DIR%

if not exist accelerator_sharedmem.cpp (
    echo ERROR: accelerator_sharedmem.cpp not found!
    pause
    exit /b 1
)

echo Installing Python dependencies...
%PYTHON_HOME%\python.exe -m pip install pybind11 numpy >nul 2>&1

echo Getting Python paths...
for /f "delims=" %%i in ('%PYTHON_HOME%\python.exe -c "import sysconfig; print(sysconfig.get_path('include'))"') do set PYTHON_INCLUDE=%%i
for /f "delims=" %%i in ('%PYTHON_HOME%\python.exe -c "import pybind11; print(pybind11.get_include())"') do set PYBIND11_INCLUDE=%%i
for /f "delims=" %%i in ('%PYTHON_HOME%\python.exe -c "import numpy; print(numpy.get_include())"') do set NUMPY_INCLUDE=%%i

echo.
echo Compiling with AVX-512 optimizations...
echo Using /arch:AVX512 (Visual Studio 2022 supports this!)
cl /MD /O2 /Ob2 /Oi /Ot /Oy /GT /GL /GS- /fp:fast /arch:AVX512 ^
    /I"%PYTHON_INCLUDE%" ^
    /I"%PYBIND11_INCLUDE%" ^
    /I"%NUMPY_INCLUDE%" ^
    /I"deps\include" ^
    /I"deps\opencv\include" ^
    /I"deps\tesseract\include" ^
    /I"deps\leptonica\include" ^
    /D_CRT_SECURE_NO_WARNINGS ^
    /DNDEBUG ^
    /D__AVX512F__ ^
    /c accelerator_sharedmem.cpp

if %ERRORLEVEL% NEQ 0 (
    echo ERROR: Compilation failed!
    echo.
    echo Trying without explicit AVX512 defines...
    cl /MD /O2 /Ob2 /Oi /Ot /Oy /GT /GL /GS- /fp:fast /arch:AVX512 ^
        /I"%PYTHON_INCLUDE%" ^
        /I"%PYBIND11_INCLUDE%" ^
        /I"%NUMPY_INCLUDE%" ^
        /I"deps\include" ^
        /I"deps\opencv\include" ^
        /I"deps\tesseract\include" ^
        /I"deps\leptonica\include" ^
        /D_CRT_SECURE_NO_WARNINGS ^
        /DNDEBUG ^
        /c accelerator_sharedmem.cpp
    
    if %ERRORLEVEL% NEQ 0 (
        echo ERROR: Compilation completely failed!
        pause
        exit /b 1
    )
)

echo Linking with AVX-512 optimized libraries...
link /DLL /OUT:bin\accelerator_sharedmem.pyd ^
    /LIBPATH:"%PYTHON_HOME%\libs" ^
    /LIBPATH:"deps\lib" ^
    /LIBPATH:"deps\opencv\x64\vc17\lib" ^
    /LIBPATH:"deps\tesseract\lib" ^
    /LIBPATH:"deps\leptonica\lib" ^
    accelerator_sharedmem.obj ^
    python311.lib ^
    opencv_world*.lib ^
    tesseract*.lib ^
    leptonica*.lib ^
    /LTCG

if %ERRORLEVEL% NEQ 0 (
    echo ERROR: Linking failed!
    pause
    exit /b 1
)

del accelerator_sharedmem.obj
echo [OK] accelerator_sharedmem.pyd built with AVX-512!

echo.
echo ============================================================
echo PART 6 COMPLETE - PYD built with AVX-512
echo ============================================================
pause

REM ====================================================================
REM FINAL TEST
REM ====================================================================
echo.
echo ============================================================
echo FINAL TEST - AVX-512 BUILD
echo ============================================================
echo.

cd bin

echo Testing module load...
%PYTHON_HOME%\python.exe -c "import accelerator_sharedmem; print('[SUCCESS] AVX-512 optimized module loads!')" 2>&1

if %ERRORLEVEL% NEQ 0 (
    echo [FAILED] Module won't load
    %PYTHON_HOME%\python.exe -c "import accelerator_sharedmem" 2>&1
) else (
    echo.
    echo Testing OCR pipeline initialization...
    %PYTHON_HOME%\python.exe -c "import accelerator_sharedmem; a = accelerator_sharedmem.AcceleratorSharedMemPipeline(); print('[SUCCESS] AVX-512 OCR pipeline ready!')" 2>&1
    
    echo.
    echo ============================================================
    echo COMPLETE SUCCESS - AVX-512 BUILD!
    echo ============================================================
    echo.
    echo Everything built with AVX-512 optimizations:
    echo - OpenCV %OPENCV_VERSION% with /arch:AVX512
    echo - Leptonica %LEPTONICA_VERSION% with /arch:AVX512
    echo - Tesseract %TESSERACT_VERSION% with /arch:AVX512
    echo - accelerator_sharedmem.pyd with /arch:AVX512
    echo.
    echo The 9950X3D supports AVX-512 with:
    echo - F (Foundation)
    echo - CD (Conflict Detection)
    echo - BW (Byte and Word)
    echo - DQ (Doubleword and Quadword)
    echo - VL (Vector Length)
    echo.
    echo Ready for MAXIMUM PERFORMANCE on the BEAST!
)

echo.
pause