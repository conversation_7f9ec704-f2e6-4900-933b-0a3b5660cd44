@echo off
REM ====================================================================
REM FIX OCR ACCELERATOR - COMPLETE SOLUTION
REM ====================================================================

echo ============================================================
echo FIXING OCR ACCELERATOR - COMPLETE SOLUTION
echo ============================================================
echo.

set BUILD_DIR=C:\TANK\ocr_accelerator
set VCPKG_BIN=C:\tools\vcpkg\installed\x64-windows\bin
set PYTHON_HOME=C:\Python311

cd /d %BUILD_DIR%

REM ====================================================================
REM STEP 1: ENSURE SOURCE FILE EXISTS
REM ====================================================================
echo Step 1: Checking source file...
if not exist accelerator_sharedmem.cpp (
    echo [ERROR] Missing accelerator_sharedmem.cpp
    echo Copy from backup or GitHub repo!
    pause
    exit /b 1
)
echo [OK] Source file found

REM ====================================================================
REM STEP 2: BUILD THE PYD (DIRECT COMPILATION - NO CMAKE)
REM ====================================================================
echo.
echo Step 2: Building accelerator_sharedmem.pyd...
echo.

REM Get Python paths
for /f "delims=" %%i in ('%PYTHON_HOME%\python.exe -c "import sysconfig; print(sysconfig.get_path('include'))"') do set PYTHON_INCLUDE=%%i
for /f "delims=" %%i in ('%PYTHON_HOME%\python.exe -c "import pybind11; print(pybind11.get_include())"') do set PYBIND11_INCLUDE=%%i
for /f "delims=" %%i in ('%PYTHON_HOME%\python.exe -c "import numpy; print(numpy.get_include())"') do set NUMPY_INCLUDE=%%i

REM Compile
echo Compiling...
cl /MD /O2 /GL /GS- /fp:fast /arch:AVX2 ^
    /I"%PYTHON_INCLUDE%" ^
    /I"%PYBIND11_INCLUDE%" ^
    /I"%NUMPY_INCLUDE%" ^
    /I"deps\include" ^
    /I"deps\opencv\include" ^
    /I"deps\tesseract\include" ^
    /I"deps\leptonica\include" ^
    /D_CRT_SECURE_NO_WARNINGS ^
    /DNDEBUG ^
    /c accelerator_sharedmem.cpp

if %ERRORLEVEL% NEQ 0 (
    echo [ERROR] Compilation failed!
    echo Installing pybind11 and numpy...
    %PYTHON_HOME%\python.exe -m pip install pybind11 numpy
    pause
    exit /b 1
)

REM Link
echo Linking...
link /DLL /OUT:bin\accelerator_sharedmem.pyd ^
    /LIBPATH:"%PYTHON_HOME%\libs" ^
    /LIBPATH:"deps\lib" ^
    /LIBPATH:"deps\opencv\x64\vc17\lib" ^
    /LIBPATH:"deps\tesseract\lib" ^
    /LIBPATH:"deps\leptonica\lib" ^
    accelerator_sharedmem.obj ^
    python311.lib ^
    opencv_world4100.lib ^
    tesseract55.lib ^
    leptonica-1.85.0.lib ^
    /LTCG

if %ERRORLEVEL% NEQ 0 (
    echo [ERROR] Linking failed!
    echo.
    echo Checking for library files...
    dir deps\opencv\x64\vc17\lib\opencv*.lib 2>nul
    dir deps\tesseract\lib\tesseract*.lib 2>nul
    dir deps\leptonica\lib\leptonica*.lib 2>nul
    pause
    exit /b 1
)

del accelerator_sharedmem.obj
echo [OK] PYD built successfully

REM ====================================================================
REM STEP 3: COPY ALL MISSING DLLS FROM VCPKG
REM ====================================================================
echo.
echo Step 3: Copying missing DLLs from vcpkg...
echo.

cd bin

REM Core image libraries
for %%D in (libpng16.dll jpeg62.dll turbojpeg.dll tiff.dll libwebp.dll gif.dll openjp2.dll zlib1.dll) do (
    if not exist "%%D" (
        if exist "%VCPKG_BIN%\%%D" (
            copy "%VCPKG_BIN%\%%D" . >nul
            echo [COPIED] %%D
        )
    )
)

REM Additional dependencies
for %%D in (liblzma.dll bz2.dll libcrypto-3-x64.dll libssl-3-x64.dll libcurl.dll sqlite3.dll libexpat.dll) do (
    if not exist "%%D" (
        if exist "%VCPKG_BIN%\%%D" (
            copy "%VCPKG_BIN%\%%D" . >nul
            echo [COPIED] %%D
        )
    )
)

REM Copy with wildcards for version variations
xcopy /Y "%VCPKG_BIN%\zlib*.dll" . >nul 2>&1
xcopy /Y "%VCPKG_BIN%\libpng*.dll" . >nul 2>&1
xcopy /Y "%VCPKG_BIN%\jpeg*.dll" . >nul 2>&1
xcopy /Y "%VCPKG_BIN%\tiff*.dll" . >nul 2>&1
xcopy /Y "%VCPKG_BIN%\libwebp*.dll" . >nul 2>&1
xcopy /Y "%VCPKG_BIN%\leptonica*.dll" . >nul 2>&1

REM ====================================================================
REM STEP 4: COPY OUR CUSTOM BUILT DLLS
REM ====================================================================
echo.
echo Step 4: Ensuring our custom DLLs are in place...

if not exist opencv_world4100.dll (
    if exist ..\deps\opencv\x64\vc17\bin\opencv_world4100.dll (
        copy ..\deps\opencv\x64\vc17\bin\opencv_world4100.dll . >nul
        echo [COPIED] opencv_world4100.dll
    )
)

if not exist tesseract55.dll (
    if exist ..\deps\tesseract\bin\tesseract55.dll (
        copy ..\deps\tesseract\bin\tesseract55.dll . >nul
        echo [COPIED] tesseract55.dll
    )
)

if not exist leptonica-1.85.0.dll (
    if exist ..\deps\leptonica\bin\leptonica-1.85.0.dll (
        copy ..\deps\leptonica\bin\leptonica-1.85.0.dll . >nul
        echo [COPIED] leptonica-1.85.0.dll
    )
)

REM ====================================================================
REM STEP 5: TEST THE MODULE
REM ====================================================================
echo.
echo Step 5: Testing the module...
echo.

%PYTHON_HOME%\python.exe -c "import accelerator_sharedmem; print('[SUCCESS] Module loads correctly!')" 2>&1

if %ERRORLEVEL% NEQ 0 (
    echo.
    echo [FAILED] Module won't load. Trying to diagnose...
    echo.
    echo Attempting detailed error:
    %PYTHON_HOME%\python.exe -c "import ctypes; ctypes.CDLL('./accelerator_sharedmem.pyd')" 2>&1
    echo.
    echo DLLs in bin folder:
    dir *.dll /B | sort
    echo.
    echo To diagnose further, use Dependency Walker or:
    echo dumpbin /dependents accelerator_sharedmem.pyd
) else (
    echo.
    echo ============================================================
    echo SUCCESS! OCR ACCELERATOR IS WORKING!
    echo ============================================================
    echo.
    echo The module is ready for deployment to BEAST.
    echo All dependencies are satisfied.
)

echo.
pause