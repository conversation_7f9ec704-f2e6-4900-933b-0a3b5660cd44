@echo off
REM ====================================================================
REM AUTOMATIC VS BUILD TOOLS ENVIRONMENT SETUP
REM ====================================================================
REM This script automatically sets up the build environment
REM Works with VS Build Tools at the path you found
REM ====================================================================

echo ============================================================
echo SETTING UP VS BUILD TOOLS ENVIRONMENT
echo ============================================================
echo.

set VS_PATH=C:\Program Files (x86)\Microsoft Visual Studio\2022\BuildTools

REM Check if already in developer prompt
where cl.exe >nul 2>&1
if %ERRORLEVEL% EQU 0 (
    echo Build environment already configured!
    echo.
    where cl.exe
    cl.exe 2>&1 | findstr /C:"Version"
) else (
    echo Initializing VS Build Tools environment...
    
    if exist "%VS_PATH%\VC\Auxiliary\Build\vcvarsall.bat" (
        echo Found vcvarsall.bat
        echo Setting up x64 build environment...
        echo.
        
        REM Initialize the environment
        call "%VS_PATH%\VC\Auxiliary\Build\vcvarsall.bat" x64
        
        echo.
        echo Environment configured!
        echo.
        
        REM Verify it worked
        where cl.exe >nul 2>&1
        if %ERRORLEVEL% EQU 0 (
            echo SUCCESS! Compiler is now available:
            where cl.exe
            echo.
            cl.exe 2>&1 | findstr /C:"Version"
        ) else (
            echo ERROR: Environment setup failed!
        )
    ) else (
        echo ERROR: vcvarsall.bat not found!
        echo Expected at: %VS_PATH%\VC\Auxiliary\Build\vcvarsall.bat
        echo.
        echo Please check your VS Build Tools installation
    )
)

echo.
echo ============================================================
echo You can now run build scripts in this window
echo ============================================================
echo.

REM Keep the window open with environment active
cmd /k