# main.py

import sys
import os

# This ensures that no matter where you run this from, 'core', 'utils', etc. can be found.
project_root = os.path.dirname(os.path.abspath(__file__))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

import argparse
import logging
import time
import signal
# from logging.handlers import RotatingFileHandler  # Disabled rotation for consolidated logging

# Import the clean DI architecture
from core.dependency_injection import DIContainer
from core.di_registration import register_all_services
from interfaces.core.application import IApplicationCore as DI_ApplicationCore
from utils.global_config import load_global_config

# Thread monitoring removed for production stability

# No longer need telemetry service imports - that's handled by start_core.bat

def setup_logging(config):
    """
    Sets up a robust, consolidated logging system for the application.
    Note: Log rotation disabled for easier debugging and consolidated output.
    """
    log_level_str = getattr(config, 'LOG_LEVEL', 'INFO').upper()
    log_level = logging.getLevelName(log_level_str)
    log_dir = getattr(config, 'LOG_BASE_DIR', 'logs')
    log_file = getattr(config, 'LOG_FILE_MAIN', 'tank_main.log')

    # Ensure the log directory exists
    os.makedirs(log_dir, exist_ok=True)
    log_path = os.path.join(log_dir, log_file)

    # Create a formatter that includes thread names
    formatter = logging.Formatter(
        '%(asctime)s - %(threadName)-15s - %(name)-30s - %(levelname)-8s - %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )

    # Configure root logger
    root_logger = logging.getLogger()
    root_logger.setLevel(log_level)
    
    # Remove any existing handlers to avoid duplicate logs
    if root_logger.hasHandlers():
        root_logger.handlers.clear()

    # Add console logger
    console_handler = logging.StreamHandler()
    console_handler.setFormatter(formatter)
    console_handler.setLevel(log_level)
    root_logger.addHandler(console_handler)

    logging.info("--- Logging system initialized ---")
    logging.info(f"Log Level: {log_level_str}")
    logging.info("File logging disabled for long test run")
    
    # Reduce verbosity of noisy libraries
    logging.getLogger("websockets").setLevel(logging.WARNING)
    logging.getLogger("asyncio").setLevel(logging.WARNING)
    logging.getLogger("urllib3").setLevel(logging.WARNING)


def main():
    """
    Main entry point for the TANK application.
    """
    # Parse command-line arguments
    parser = argparse.ArgumentParser(description="TANK Core Trading Engine")
    parser.add_argument("--headless", action="store_true", 
                       help="Run with telemetry disabled (NullCorrelationLogger)")
    parser.add_argument("--replay", action="store_true", 
                       help="Run with mock service configurations for backtesting")
    parser.add_argument("--tank-broadcasting", action="store_true",
                       help="Run with all real services and enhanced telemetry broadcasting")
    args = parser.parse_args()
    
    logging.info("--- TANK Application Starting ---")
    if args.headless:
        logging.info("Running in HEADLESS mode - telemetry disabled")
    elif args.replay:
        logging.info("Running in REPLAY mode - mock services configuration")
    elif getattr(args, 'tank_broadcasting', False):
        logging.info("Running in TANK BROADCASTING mode - real services with enhanced telemetry")
    else:
        logging.info("Running in LIVE mode - full production configuration")

    # Thread monitoring removed for production stability
    
    # 1. Load configuration first
    try:
        config_path = "utils/control.json"
        config = load_global_config(config_path)
        
        # Override telemetry settings based on command-line arguments
        if args.headless:
            config.enable_intellisense_logging = False
            config.ENABLE_IPC_DATA_DUMP = False
            logging.info("Command-line override: Telemetry disabled for headless mode")
        elif args.replay:
            # For replay mode, we want telemetry but with mock service endpoints
            config.enable_intellisense_logging = True
            config.ENABLE_IPC_DATA_DUMP = True
            # Mock service configurations would be set here or in environment
            logging.info("Command-line override: Configured for replay mode")
        elif getattr(args, 'tank_broadcasting', False):
            # For tank broadcasting mode, enable both telemetry flags with real services
            config.enable_intellisense_logging = True
            config.ENABLE_IPC_DATA_DUMP = True
            logging.info("Command-line override: Enhanced telemetry broadcasting enabled")
            
        setup_logging(config)
    except Exception as e:
        print(f"CRITICAL: Could not load configuration file. Error: {e}")
        return

    # 2. Create the Dependency Injection Container
    container = DIContainer()
    
    # 3. Register all services with command-line args for proper mode handling
    register_all_services(container, args)
    
    # --- PHASE 0 HANDSHAKE ---
    logging.info("MAIN: Performing prerequisite broker handshake...")
    from interfaces.broker.services import IBrokerService
    broker = container.resolve(IBrokerService)
    if not broker.establish_connection():
        logging.critical("FATAL: Could not establish connection with broker. Application will not start.")
        return  # Exit the application
    logging.info("MAIN: Broker connection successful.")
    
    # FINAL ARCHITECTURE: Create shared queue and start OCR telemetry process
    from multiprocessing import Process, Queue
    telemetry_process = None
    ocr_telemetry_process = None
    telemetry_queue = None
    background_processes = []
    
    # Determine if telemetry should be active
    enable_intellisense = getattr(config, 'enable_intellisense_logging', False)
    enable_ipc_dump = getattr(config, 'ENABLE_IPC_DATA_DUMP', False)
    
    # Only start telemetry if ALL conditions are met
    should_start_telemetry = enable_intellisense and enable_ipc_dump
    logging.info(f"Telemetry mode decision: enable_intellisense={enable_intellisense}, "
                    f"enable_ipc_dump={enable_ipc_dump}, should_start_telemetry={should_start_telemetry}")
    
    # Only create telemetry infrastructure if needed
    if should_start_telemetry:
        # Create the shared queue for OCR -> Telemetry communication
        telemetry_queue = Queue(maxsize=1000)
        logging.info("Created shared telemetry queue for OCR process isolation")
        
        # Start the OCR telemetry process
        logging.info("FINAL ARCHITECTURE: Starting OCR telemetry process...")
        from modules.ocr.telemetry_process_main import run_telemetry_service_process
        
        log_base_dir = getattr(config, 'LOG_BASE_DIR', 'logs')
        ocr_telemetry_process = Process(
            target=run_telemetry_service_process,
            args=(telemetry_queue, log_base_dir, config),
            name="OCRTelemetryProcess"
        )
        ocr_telemetry_process.daemon = True
        ocr_telemetry_process.start()
        background_processes.append(ocr_telemetry_process)
        logging.info(f"OCR Telemetry Process started with PID {ocr_telemetry_process.pid}")
    else:
        logging.info("Telemetry is DISABLED - OCR will run in hot-path-only mode")
        telemetry_queue = None  # Explicitly None when telemetry is disabled
    
    # Register the telemetry queue with the DI container for OCRProcessManager
    # This is needed so OCRProcessManager can pass it to the OCR subprocess
    container.register_instance('telemetry_queue', telemetry_queue)
    
    # 7. Resolve the top-level application coordinator
    # The DI container will build the entire application graph from here.
    app_core = container.resolve(DI_ApplicationCore)
    
    try:
        # 4. Start the application
        app_core.start()
        
        # --- THIS IS THE CRITICAL FIX ---
        # 5. Main Execution Loop to Keep the Process Alive
        logging.info("System is running. Press Ctrl+C to stop.")
        while True:
            # We check if the application core is still considered "ready"
            # This allows for a programmatic shutdown if needed.
            if hasattr(app_core, 'is_ready') and not app_core.is_ready:
                logging.warning("ApplicationCore is no longer ready. Shutting down main loop.")
                break
            time.sleep(1)  # Block the main thread, polling every second.
        # --- END OF FIX ---
            
    except KeyboardInterrupt:
        logging.info("Keyboard interrupt received. Shutting down...")
    except Exception as e:
        logging.critical("An unhandled exception occurred in the main execution block.", exc_info=True)
    finally:
        # 6. Stop the application
        if 'app_core' in locals() and app_core:
            logging.info("Initiating application shutdown...")
            app_core.stop()
        
        # FINAL ARCHITECTURE: Stop OCR Telemetry process (if it was started)
        if 'ocr_telemetry_process' in locals() and ocr_telemetry_process and ocr_telemetry_process.is_alive():
            logging.info("FINAL ARCHITECTURE: Stopping OCR Telemetry process...")
            ocr_telemetry_process.terminate()
            ocr_telemetry_process.join(timeout=2.0)
            if ocr_telemetry_process.is_alive():
                logging.warning("OCR Telemetry process did not stop gracefully, killing...")
                ocr_telemetry_process.kill()
            logging.info("FINAL ARCHITECTURE: OCR Telemetry process stopped")
        
        logging.info("--- TANK Application Shutdown Complete ---")

def setup_surgical_signal_handlers():
    """Setup surgical signal handlers for zero phantom process shutdown."""
    from utils.process_cleanup import create_surgical_signal_handler
    
    # Create surgical cleanup manager and signal handler
    cleanup_manager, signal_handler = create_surgical_signal_handler("TANK Core")
    
    # Register signal handlers
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    if hasattr(signal, 'SIGBREAK'):
        signal.signal(signal.SIGBREAK, signal_handler)
    
    return cleanup_manager, signal_handler

if __name__ == "__main__":
    # Setup surgical signal handlers before starting
    cleanup_manager, shutdown_handler = setup_surgical_signal_handlers()
    
    try:
        main()
    except KeyboardInterrupt:
        logging.info("🚀 TANK Core interrupted - performing surgical shutdown")
        # Thread monitoring removed
        cleanup_manager.surgical_shutdown()
        sys.exit(0)
    except Exception as e:
        logging.error(f"❌ TANK Core fatal error: {e}")
        cleanup_manager.surgical_shutdown()
        sys.exit(1)
    finally:
        pass  # Thread monitoring removed