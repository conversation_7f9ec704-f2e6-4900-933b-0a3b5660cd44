@echo off
REM Quick script to activate virtual environment

cd /d "C:\TANK"

if exist ".venv\Scripts\activate.bat" (
    echo Activating .venv...
    call .venv\Scripts\activate.bat
    echo.
    echo Virtual environment activated!
    echo Python location:
    where python
    echo.
    echo Python version:
    python --version
    echo.
    echo Testing imports:
    python -c "import websockets; print('SUCCESS: websockets found')"
    python -c "import numpy; print('SUCCESS: numpy found')"
    echo.
    cmd /k
) else (
    echo ERROR: .venv not found at C:\TANK\.venv
    pause
)